# 产品管理模块设计文档

## 1. 概述

### 1.1 设计目标
基于玻璃深加工行业的MTO（Make-to-Order）生产模式，设计一套完整的产品管理系统，支持从客户订单驱动的产品配置、BOM生成到生产工单创建的全流程管理。

### 1.2 核心理念
- **组件**：生产的基本单元，可直接与物料分类映射
- **构件**：组件的集合体，体现装配与层级关系
- **产品结构**：描述构件、组件信息的层级关系与装配逻辑
- **产品**：基于产品结构的具体产品定义，包含物料分类映射
- **BOM**：分为报价BOM（基于物料分类）和生产BOM（具体物料变体）

### 1.3 业务价值
- 支持复杂产品结构的层级化管理
- 实现订单驱动的个性化产品配置
- 提供精确的成本核算和报价支持
- 确保生产BOM的物料精准性
- 支持产品全生命周期的版本管理和追溯

## 2. 数据模型设计

### 2.1 核心实体关系

```mermaid
erDiagram
    ProductStructure ||--o{ Product : "一对多"
    Product ||--o{ QuoteBOM : "一对多"
    Product ||--o{ ProductionBOM : "一对多"
    QuoteBOM ||--o| ProductionBOM : "可转换"
    ProductStructure ||--o{ Assembly : "包含"
    Assembly ||--o{ Component : "包含"
    Assembly ||--o{ Assembly : "嵌套"
    Component }o--|| MaterialCategory : "映射"
    ProductionBOMItem }o--|| MaterialVariant : "引用"
```

### 2.2 TypeScript 接口定义

#### 2.2.1 基础类型

```typescript
// 版本化实体基类
export interface VersionedEntity {
  id: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

// 状态枚举
export enum LifecycleStatus {
  DRAFT = 'draft',           // 草稿
  ACTIVE = 'active',         // 激活
  DEPRECATED = 'deprecated', // 已弃用
  ARCHIVED = 'archived'      // 已归档
}

export enum BOMStatus {
  DRAFT = 'draft',       // 草稿
  CONFIRMED = 'confirmed', // 已确认
  APPROVED = 'approved',   // 已批准
  RELEASED = 'released'    // 已下发
}
```

#### 2.2.2 组件定义

```typescript
// 组件定义（生产基本单元）
export interface Component extends VersionedEntity {
  code: string;                    // 组件编码
  name: string;                    // 组件名称
  description?: string;            // 描述
  materialCategoryId: string;      // 关联物料分类ID
  materialCategoryName: string;    // 物料分类名称（冗余字段）
  
  // 参数定义
  parameters: ComponentParameter[];
  
  // 计算规则
  quantityFormula?: string;        // 数量计算公式
  costFormula?: string;           // 成本计算公式
  
  // 约束条件
  constraints: ComponentConstraint[];
  
  // 状态
  status: LifecycleStatus;
  
  // 扩展属性
  properties: Record<string, any>;
}

// 组件参数
export interface ComponentParameter {
  id: string;
  name: string;
  type: 'number' | 'string' | 'boolean' | 'select';
  unit?: string;
  defaultValue?: any;
  minValue?: number;
  maxValue?: number;
  options?: string[];
  required: boolean;
  description?: string;
}

// 组件约束
export interface ComponentConstraint {
  id: string;
  name: string;
  expression: string;             // 约束表达式
  errorMessage: string;
  severity: 'error' | 'warning';
}
```

#### 2.2.3 构件定义

```typescript
// 构件定义（组件集合体）
export interface Assembly extends VersionedEntity {
  code: string;
  name: string;
  description?: string;
  
  // 组件实例
  componentInstances: ComponentInstance[];
  
  // 子构件
  subAssemblies: AssemblyInstance[];
  
  // 装配参数
  assemblyParameters: ComponentParameter[];
  
  // 状态
  status: LifecycleStatus;
  
  // 装配约束
  assemblyConstraints: ComponentConstraint[];
}

// 组件实例
export interface ComponentInstance {
  id: string;
  componentId: string;
  componentCode: string;
  componentName: string;
  
  // 实例参数值
  parameterValues: Record<string, any>;
  
  // 位置信息
  position?: {
    x: number;
    y: number;
    z: number;
    rotation?: number;
  };
  
  // 数量
  quantity: number;
  quantityFormula?: string;
  
  // 是否可选
  optional: boolean;
}

// 构件实例
export interface AssemblyInstance {
  id: string;
  assemblyId: string;
  assemblyCode: string;
  assemblyName: string;
  
  // 实例参数值
  parameterValues: Record<string, any>;
  
  // 位置信息
  position?: {
    x: number;
    y: number;
    z: number;
    rotation?: number;
  };
  
  // 数量
  quantity: number;
  quantityFormula?: string;
  
  // 是否可选
  optional: boolean;
}
```

## 3. 路由与导航设计

### 3.1 路由结构

```typescript
// 产品管理模块路由
const productRoutes: RouteRecordRaw[] = [
  // 产品管理主页（概览）
  {
    path: "/product-management",
    name: "product-management",
    component: () => import("../views/product/ProductManagementOverview.vue"),
    meta: {
      title: "产品管理",
      icon: "Package",
      requiresAuth: true,
    },
  },
  
  // 组件管理
  {
    path: "/product-management/components",
    name: "product-components",
    component: () => import("../views/product/Components.vue"),
    meta: {
      title: "组件管理",
      icon: "Component",
      requiresAuth: true,
      parent: "product-management",
    },
  },
  
  // 构件管理
  {
    path: "/product-management/assemblies",
    name: "product-assemblies",
    component: () => import("../views/product/Assemblies.vue"),
    meta: {
      title: "构件管理",
      icon: "Layers",
      requiresAuth: true,
      parent: "product-management",
    },
  },
  
  // 产品结构管理
  {
    path: "/product-management/structures",
    name: "product-structures",
    component: () => import("../views/product/ProductStructures.vue"),
    meta: {
      title: "产品结构",
      icon: "Workflow",
      requiresAuth: true,
      parent: "product-management",
    },
  },
  
  // 产品管理
  {
    path: "/product-management/products",
    name: "products",
    component: () => import("../views/product/Products.vue"),
    meta: {
      title: "产品管理",
      icon: "PackageSearch",
      requiresAuth: true,
      parent: "product-management",
    },
  },
  
  // 报价BOM管理
  {
    path: "/product-management/quote-boms",
    name: "quote-boms",
    component: () => import("../views/product/QuoteBOMs.vue"),
    meta: {
      title: "报价BOM",
      icon: "BadgeDollarSign",
      requiresAuth: true,
      parent: "product-management",
    },
  },
  
  // 生产BOM管理
  {
    path: "/product-management/production-boms",
    name: "production-boms",
    component: () => import("../views/product/ProductionBOMs.vue"),
    meta: {
      title: "生产BOM",
      icon: "Hammer",
      requiresAuth: true,
      parent: "product-management",
    },
  },
];
```

### 3.2 侧边栏导航结构

```typescript
// 导航菜单配置
export const productManagementMenu = {
  id: "product-management",
  title: "产品管理",
  icon: "Package",
  children: [
    {
      id: "product-components",
      title: "组件管理",
      icon: "Component",
      path: "/product-management/components",
    },
    {
      id: "product-assemblies", 
      title: "构件管理",
      icon: "Layers",
      path: "/product-management/assemblies",
    },
    {
      id: "product-structures",
      title: "产品结构",
      icon: "Workflow", 
      path: "/product-management/structures",
    },
    {
      id: "products",
      title: "产品管理",
      icon: "PackageSearch",
      path: "/product-management/products",
    },
    {
      id: "quote-boms",
      title: "报价BOM",
      icon: "BadgeDollarSign",
      path: "/product-management/quote-boms",
    },
    {
      id: "production-boms",
      title: "生产BOM", 
      icon: "Hammer",
      path: "/product-management/production-boms",
    },
  ],
};
```

## 4. 页面功能设计

### 4.1 组件管理页面

#### 4.1.1 列表展示
- **表格字段**：
  - 组件编码、组件名称、物料分类、状态、版本、更新时间、操作
- **筛选条件**：
  - 物料分类、状态、关键字搜索、创建时间范围
- **排序**：支持按编码、名称、更新时间排序
- **分页**：支持分页加载

#### 4.1.2 CRUD操作
- **新增组件**：
  - 基本信息录入（编码、名称、描述）
  - 物料分类选择
  - 参数定义（类型、单位、默认值、约束）
  - 计算公式设置
  - 约束条件配置
- **编辑组件**：支持所有字段编辑，版本自动递增
- **删除组件**：软删除，检查关联关系
- **复制组件**：基于现有组件创建新版本

#### 4.1.3 详情查看
- **基本信息**：编码、名称、描述、物料分类
- **参数列表**：参数名称、类型、单位、默认值、约束
- **使用情况**：被哪些构件引用
- **版本历史**：历史版本列表和变更记录

### 4.2 构件管理页面

#### 4.2.1 列表展示
- **表格字段**：
  - 构件编码、构件名称、组件数量、子构件数量、状态、版本、更新时间、操作
- **筛选条件**：
  - 状态、关键字搜索、创建时间范围
- **树形展示**：支持构件层级树形展示

#### 4.2.2 CRUD操作
- **新增构件**：
  - 基本信息录入
  - 组件选择和配置（数量、参数值、位置）
  - 子构件选择和配置
  - 装配约束设置
- **编辑构件**：支持结构调整和参数修改
- **删除构件**：检查被产品结构引用情况
- **构件预览**：3D或2D结构预览

#### 4.2.3 详情查看
- **结构树**：组件和子构件的层级关系
- **参数配置**：各组件实例的参数值
- **使用情况**：被哪些产品结构引用

### 4.3 产品结构管理页面

#### 4.3.1 列表展示
- **表格字段**：
  - 结构编码、结构名称、产品类别、关联产品数、状态、版本、更新时间、操作
- **筛选条件**：
  - 产品类别、状态、关键字搜索、标签筛选

#### 4.3.2 CRUD操作
- **新增产品结构**：
  - 基本信息录入
  - 根构件选择
  - 全局参数定义
  - 计算公式配置
  - 业务规则设置
  - 文档附件上传
- **编辑产品结构**：支持结构调整和规则修改
- **版本管理**：创建新版本、版本比较、版本回退
- **结构预览**：可视化结构展示

#### 4.3.3 详情查看
- **结构概览**：基本信息和统计数据
- **结构树**：完整的产品结构层级
- **参数列表**：全局参数和计算公式
- **业务规则**：规则列表和触发条件
- **关联产品**：基于此结构的产品列表
- **文档附件**：相关文档和图纸

### 4.4 产品管理页面

#### 4.4.1 列表展示
- **表格字段**：
  - 产品编码、产品名称、产品结构、生命周期、报价BOM数、生产BOM数、最近订单、操作
- **筛选条件**：
  - 产品结构、生命周期、产品分类、标签筛选、关键字搜索

#### 4.4.2 CRUD操作
- **新增产品**：
  - 基本信息录入
  - 产品结构选择
  - 组件物料映射配置
  - 默认参数值设置
  - 生命周期状态设置
- **编辑产品**：支持信息更新和映射调整
- **生命周期管理**：状态流转和审批
- **快捷操作**：
  - 生成报价BOM
  - 生成生产BOM
  - 创建产品配置

#### 4.4.3 详情查看
- **基本信息**：产品详情和分类信息
- **结构信息**：关联的产品结构详情
- **物料映射**：组件到物料分类的映射关系
- **BOM列表**：报价BOM和生产BOM列表
- **订单历史**：相关订单记录
- **统计分析**：使用频率和成本分析

### 4.5 报价BOM管理页面

#### 4.5.1 列表展示
- **表格字段**：
  - BOM编码、BOM名称、关联产品、总成本、状态、创建时间、有效期、操作
- **筛选条件**：
  - 关联产品、状态、创建时间范围、成本范围

#### 4.5.2 CRUD操作
- **新增报价BOM**：
  - 产品选择
  - 配置参数设置
  - 自动生成BOM项目
  - 成本计算和调整
  - 有效期设置
- **编辑报价BOM**：支持BOM项目调整和成本修改
- **状态管理**：草稿→确认→批准流程
- **转换操作**：转为生产BOM

#### 4.5.3 详情查看
- **BOM树**：层级化BOM结构展示
- **成本分析**：材料成本、人工成本、制造费用分解
- **配置快照**：生成BOM时的参数配置
- **变更历史**：BOM修改记录

### 4.6 生产BOM管理页面

#### 4.6.1 列表展示
- **表格字段**：
  - BOM编码、BOM名称、关联产品、来源报价BOM、总成本、状态、生效时间、操作
- **筛选条件**：
  - 关联产品、来源报价BOM、状态、生效时间范围

#### 4.6.2 CRUD操作
- **新增生产BOM**：
  - 基于报价BOM转换
  - 物料变体选择
  - 库存检查和预留
  - 替代物料配置
  - 工艺路线关联
- **编辑生产BOM**：支持物料调整和数量修改
- **状态管理**：草稿→确认→批准→下发流程
- **下发操作**：生成生产工单

#### 4.6.3 详情查看
- **BOM树**：具体物料变体的BOM结构
- **库存状态**：各物料的库存可用量和短缺情况
- **成本分析**：实际物料成本分析
- **工艺路线**：关联的生产工艺流程
- **工单历史**：基于此BOM生成的工单记录

## 5. Store状态管理设计

### 5.1 Store架构

```typescript
// 组件Store
export const useComponentStore = defineStore('component', () => {
  // 状态
  const components = ref<Component[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 缓存
  const componentMap = computed(() => {
    return components.value.reduce((map, component) => {
      map[component.id] = component;
      return map;
    }, {} as Record<string, Component>);
  });

  // 方法
  const loadComponents = async (filters?: ComponentFilter) => {
    loading.value = true;
    try {
      // 加载组件数据
      const data = await componentApi.getComponents(filters);
      components.value = data;
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  const createComponent = async (component: CreateComponentRequest) => {
    const newComponent = await componentApi.createComponent(component);
    components.value.push(newComponent);
    return newComponent;
  };

  const updateComponent = async (id: string, updates: UpdateComponentRequest) => {
    const updatedComponent = await componentApi.updateComponent(id, updates);
    const index = components.value.findIndex(c => c.id === id);
    if (index >= 0) {
      components.value[index] = updatedComponent;
    }
    return updatedComponent;
  };

  const deleteComponent = async (id: string) => {
    await componentApi.deleteComponent(id);
    components.value = components.value.filter(c => c.id !== id);
  };

  return {
    // 状态
    components: readonly(components),
    loading: readonly(loading),
    error: readonly(error),

    // 计算属性
    componentMap,

    // 方法
    loadComponents,
    createComponent,
    updateComponent,
    deleteComponent,
  };
});

// 类似的Store结构适用于其他实体
export const useAssemblyStore = defineStore('assembly', () => { /* ... */ });
export const useProductStructureStore = defineStore('productStructure', () => { /* ... */ });
export const useProductStore = defineStore('product', () => { /* ... */ });
export const useQuoteBOMStore = defineStore('quoteBOM', () => { /* ... */ });
export const useProductionBOMStore = defineStore('productionBOM', () => { /* ... */ });
```

### 5.2 跨Store集成

```typescript
// 产品配置Store（集成多个Store）
export const useProductConfigurationStore = defineStore('productConfiguration', () => {
  // 依赖其他Store
  const productStore = useProductStore();
  const productStructureStore = useProductStructureStore();
  const materialVariantStore = useMaterialVariantStore();

  // 配置生成
  const generateQuoteBOM = async (productId: string, parameters: Record<string, any>) => {
    const product = productStore.productMap[productId];
    const structure = productStructureStore.structureMap[product.productStructureId];

    // 执行参数计算和规则验证
    const calculatedValues = calculateParameters(structure, parameters);
    const validationResults = validateConfiguration(structure, calculatedValues);

    if (validationResults.some(r => r.type === 'error')) {
      throw new Error('配置验证失败');
    }

    // 生成BOM项目
    const bomItems = await generateBOMItems(structure, calculatedValues);

    // 创建报价BOM
    const quoteBOM: QuoteBOM = {
      // ... BOM数据
      configurationSnapshot: calculatedValues,
      items: bomItems,
    };

    return quoteBOM;
  };

  const convertToProductionBOM = async (quoteBOMId: string) => {
    const quoteBOM = useQuoteBOMStore().bomMap[quoteBOMId];

    // 解析物料变体
    const productionItems = await Promise.all(
      quoteBOM.items.map(async (item) => {
        const materialVariants = await materialVariantStore.findByCategory(
          item.materialCategoryId
        );

        // 根据规则选择最佳物料变体
        const selectedVariant = selectBestMaterialVariant(
          materialVariants,
          quoteBOM.configurationSnapshot
        );

        return {
          ...item,
          materialVariantId: selectedVariant.id,
          materialVariantCode: selectedVariant.code,
          materialVariantName: selectedVariant.name,
          unitCost: selectedVariant.cost,
          stockInfo: {
            availableQuantity: selectedVariant.availableQuantity,
            reservedQuantity: selectedVariant.reservedQuantity,
            shortageQuantity: Math.max(0, item.actualQuantity - selectedVariant.availableQuantity),
          },
        };
      })
    );

    const productionBOM: ProductionBOM = {
      // ... 生产BOM数据
      quoteBOMId,
      items: productionItems,
    };

    return productionBOM;
  };

  return {
    generateQuoteBOM,
    convertToProductionBOM,
  };
});
```

## 6. MTO流程设计

### 6.1 业务流程概览

```mermaid
flowchart TD
    A[客户订单] --> B[订单项分析]
    B --> C[产品匹配/选择]
    C --> D[产品配置]
    D --> E[参数验证]
    E --> F{验证通过?}
    F -->|否| D
    F -->|是| G[生成报价BOM]
    G --> H[成本计算]
    H --> I[客户确认]
    I --> J{客户确认?}
    J -->|否| K[修改配置]
    K --> D
    J -->|是| L[转换生产BOM]
    L --> M[物料检查]
    M --> N[库存预留]
    N --> O[生成生产工单]
    O --> P[下发生产]
```

### 6.2 核心流程实现

#### 6.2.1 订单驱动的产品配置

```typescript
// MTO服务
export class MTOService {
  // 从订单项创建产品配置
  async createConfigurationFromOrder(
    orderItem: CustomerOrderItem,
    productId?: string
  ): Promise<ProductConfiguration> {
    // 1. 产品匹配
    let product: Product;
    if (productId) {
      product = await productApi.getProduct(productId);
    } else {
      product = await this.matchProductFromSpecifications(orderItem.specifications);
    }

    // 2. 参数映射
    const parameters = this.mapSpecificationsToParameters(
      orderItem.specifications,
      product
    );

    // 3. 创建配置
    const configuration: ProductConfiguration = {
      id: generateId(),
      productId: product.id,
      productCode: product.code,
      productName: product.name,
      configurationName: `订单配置-${orderItem.id}`,
      parameterValues: parameters,
      calculatedValues: {},
      validationResults: [],
      createdAt: new Date().toISOString(),
      createdBy: 'system',
      sourceType: 'order',
      sourceId: orderItem.id,
    };

    // 4. 参数计算和验证
    await this.calculateAndValidateConfiguration(configuration);

    return configuration;
  }

  // 规格到参数的映射
  private mapSpecificationsToParameters(
    specifications: any,
    product: Product
  ): Record<string, any> {
    const parameters: Record<string, any> = {};

    // 基础尺寸映射
    if (specifications.length) {
      parameters.window_width = specifications.length;
    }
    if (specifications.width) {
      parameters.window_height = specifications.width;
    }
    if (specifications.thickness) {
      parameters.glass_thickness = specifications.thickness;
    }

    // 玻璃类型映射
    if (specifications.glassType) {
      parameters.glass_type = specifications.glassType;
    }

    // 颜色映射
    if (specifications.color) {
      parameters.glass_color = specifications.color;
    }

    // 合并产品默认参数
    return {
      ...product.defaultParameters,
      ...parameters,
    };
  }

  // 产品匹配
  private async matchProductFromSpecifications(
    specifications: any
  ): Promise<Product> {
    // 根据规格匹配最合适的产品
    const products = await productApi.getProducts({
      category: this.inferCategoryFromSpecifications(specifications),
    });

    // 简单匹配逻辑，实际可以更复杂
    return products[0];
  }
}
```

## 7. Mock数据设计

### 7.1 数据文件结构

```
public/mock/product/
├── components.json              # 组件数据
├── assemblies.json             # 构件数据
├── product-structures.json     # 产品结构数据
├── products.json               # 产品数据
├── quote-boms.json            # 报价BOM数据
├── production-boms.json       # 生产BOM数据
├── configurations.json        # 产品配置数据
├── formulas.json             # 计算公式数据
├── business-rules.json       # 业务规则数据
└── material-mappings.json    # 物料映射数据
```

### 7.2 示例数据

#### 7.2.1 组件数据示例

```json
{
  "components": [
    {
      "id": "comp_001",
      "code": "FRAME_OUTER",
      "name": "外框型材",
      "description": "防火窗外框型材组件",
      "materialCategoryId": "cat_steel_profile",
      "materialCategoryName": "钢质型材",
      "parameters": [
        {
          "id": "param_width",
          "name": "宽度",
          "type": "number",
          "unit": "mm",
          "defaultValue": 60,
          "minValue": 50,
          "maxValue": 100,
          "required": true,
          "description": "型材宽度"
        },
        {
          "id": "param_thickness",
          "name": "厚度",
          "type": "number",
          "unit": "mm",
          "defaultValue": 3.0,
          "minValue": 2.0,
          "maxValue": 5.0,
          "required": true,
          "description": "型材壁厚"
        }
      ],
      "quantityFormula": "perimeter / 6000",
      "constraints": [
        {
          "id": "const_001",
          "name": "厚度约束",
          "expression": "param_thickness >= 2.5",
          "errorMessage": "型材厚度不能小于2.5mm",
          "severity": "error"
        }
      ],
      "status": "active",
      "version": 1,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "createdBy": "admin",
      "updatedBy": "admin",
      "properties": {
        "fireRating": "A",
        "material": "steel"
      }
    }
  ]
}
```

## 8. 实施计划

### 8.1 开发阶段

#### 阶段一：基础架构（1-2周）
1. **数据模型实现**
   - 创建 `src/types/product.ts` 完整类型定义
   - 设计 Mock 数据结构和示例数据
   - 创建基础工具函数和验证逻辑

2. **路由和导航**
   - 更新 `src/router/index.ts` 添加产品管理路由
   - 更新侧边栏导航配置
   - 创建页面骨架组件

#### 阶段二：Store和服务（2-3周）
1. **状态管理**
   - 实现各实体的 Pinia Store
   - 集成现有 materialVariant 和 metadata Store
   - 实现跨 Store 的数据关联逻辑

2. **业务服务**
   - 实现 MTO 流程服务
   - 实现 BOM 生成和转换服务
   - 实现配置计算和验证服务

#### 阶段三：页面功能（3-4周）
1. **列表页面**
   - 实现各实体的列表展示和筛选
   - 实现分页、排序、搜索功能
   - 集成 ShadCN Vue 组件

2. **CRUD功能**
   - 实现新增、编辑、删除功能
   - 实现表单验证和错误处理
   - 实现状态流转和审批功能

#### 阶段四：集成和优化（2-3周）
1. **MTO流程集成**
   - 实现订单到配置的转换
   - 实现BOM生成和工单创建
   - 集成现有MES模块

2. **用户体验优化**
   - 实现实时计算和预览
   - 优化页面性能和加载速度
   - 完善错误处理和用户反馈

### 8.2 测试策略

#### 单元测试
- 数据模型验证测试
- 计算公式和业务规则测试
- Store 状态管理测试
- 服务层业务逻辑测试

#### 集成测试
- MTO 端到端流程测试
- 跨模块数据关联测试
- 用户界面交互测试

#### 用户验收测试
- 业务流程完整性验证
- 用户体验和易用性测试
- 性能和稳定性测试

### 8.3 部署和维护

#### 部署准备
- 环境配置和依赖管理
- 数据迁移和初始化脚本
- 监控和日志配置

#### 维护计划
- 定期数据备份和恢复测试
- 性能监控和优化
- 功能迭代和bug修复

## 9. 总结

本设计文档提供了一套完整的产品管理模块解决方案，基于组件→构件→产品结构→产品→BOM的层级关系，支持MTO模式的订单驱动生产流程。

### 9.1 核心优势
- **层级清晰**：明确的组件、构件、产品结构关系
- **灵活配置**：支持参数化配置和业务规则
- **精确成本**：从报价BOM到生产BOM的精确转换
- **流程完整**：覆盖从订单到生产的完整MTO流程
- **易于扩展**：模块化设计支持功能扩展

### 9.2 技术特点
- **类型安全**：完整的TypeScript类型定义
- **状态管理**：基于Pinia的响应式状态管理
- **组件复用**：基于ShadCN Vue的组件库
- **数据驱动**：基于Mock数据的原型验证

### 9.3 业务价值
- **提升效率**：自动化的BOM生成和成本计算
- **降低错误**：规则驱动的配置验证
- **增强追溯**：完整的版本管理和快照机制
- **支持决策**：丰富的统计分析和报表功能

通过本设计的实施，将为玻璃深加工企业提供一套现代化、智能化的产品管理解决方案，有效支撑MTO生产模式的业务需求。
