#!/usr/bin/env node

/**
 * Mock数据验证脚本
 * 验证Mock数据的完整性和一致性
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Mock数据文件路径
const MOCK_DATA_DIR = path.join(__dirname, '../public/mock/product');
const DATA_FILES = [
  'components.json',
  'assemblies.json',
  'products.json',
  'quote-boms.json',
  'production-boms.json'
];

// 验证结果
const validationResults = {
  errors: [],
  warnings: [],
  info: []
};

/**
 * 添加验证结果
 */
function addResult(type, message, file = null) {
  const result = { message, file, timestamp: new Date().toISOString() };
  validationResults[type].push(result);
  
  const prefix = type === 'errors' ? '❌' : type === 'warnings' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} ${file ? `[${file}] ` : ''}${message}`);
}

/**
 * 验证JSON文件格式
 */
function validateJsonFormat(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    addResult('info', `JSON格式正确`, path.basename(filePath));
    return data;
  } catch (error) {
    addResult('errors', `JSON格式错误: ${error.message}`, path.basename(filePath));
    return null;
  }
}

/**
 * 验证必需字段
 */
function validateRequiredFields(obj, requiredFields, objName, fileName) {
  for (const field of requiredFields) {
    if (!obj.hasOwnProperty(field) || obj[field] === null || obj[field] === undefined) {
      addResult('errors', `${objName} 缺少必需字段: ${field}`, fileName);
    }
  }
}

/**
 * 验证ID格式
 */
function validateIdFormat(id, expectedPrefix, objName, fileName) {
  if (!id || typeof id !== 'string') {
    addResult('errors', `${objName} ID格式错误: ${id}`, fileName);
    return false;
  }
  
  if (!id.startsWith(expectedPrefix)) {
    addResult('warnings', `${objName} ID前缀不符合约定 (期望: ${expectedPrefix}): ${id}`, fileName);
  }
  
  return true;
}

/**
 * 验证组件数据
 */
function validateComponents(data) {
  const fileName = 'components.json';
  
  if (!data || !data.components || !Array.isArray(data.components)) {
    addResult('errors', '组件数据结构错误', fileName);
    return {};
  }
  
  const componentIds = new Set();
  const componentCodes = new Set();
  
  for (const component of data.components) {
    // 验证必需字段
    validateRequiredFields(component, [
      'id', 'code', 'name', 'componentType', 'materialCategoryId',
      'materialCategoryName', 'materialCategoryCode', 'status'
    ], '组件', fileName);
    
    // 验证ID格式
    validateIdFormat(component.id, 'comp_', '组件', fileName);
    
    // 检查ID唯一性
    if (componentIds.has(component.id)) {
      addResult('errors', `重复的组件ID: ${component.id}`, fileName);
    } else {
      componentIds.add(component.id);
    }
    
    // 检查编码唯一性
    if (componentCodes.has(component.code)) {
      addResult('errors', `重复的组件编码: ${component.code}`, fileName);
    } else {
      componentCodes.add(component.code);
    }
    
    // 验证组件类型
    const validTypes = ['frame', 'glass', 'hardware', 'seal', 'other'];
    if (!validTypes.includes(component.componentType)) {
      addResult('warnings', `未知的组件类型: ${component.componentType}`, fileName);
    }
    
    // 验证状态
    const validStatuses = ['draft', 'active', 'deprecated', 'archived'];
    if (!validStatuses.includes(component.status)) {
      addResult('warnings', `未知的组件状态: ${component.status}`, fileName);
    }
    
    // 验证参数结构
    if (component.parameters && Array.isArray(component.parameters)) {
      for (const param of component.parameters) {
        validateRequiredFields(param, [
          'id', 'name', 'displayName', 'type', 'required', 'category'
        ], '组件参数', fileName);
        
        const validParamTypes = ['number', 'string', 'boolean', 'select', 'formula'];
        if (!validParamTypes.includes(param.type)) {
          addResult('warnings', `未知的参数类型: ${param.type}`, fileName);
        }
      }
    }
    
    // 验证约束结构
    if (component.constraints && Array.isArray(component.constraints)) {
      for (const constraint of component.constraints) {
        validateRequiredFields(constraint, [
          'id', 'name', 'type', 'expression', 'errorMessage', 'severity'
        ], '组件约束', fileName);
        
        const validSeverities = ['error', 'warning', 'info'];
        if (!validSeverities.includes(constraint.severity)) {
          addResult('warnings', `未知的约束严重程度: ${constraint.severity}`, fileName);
        }
      }
    }
  }
  
  addResult('info', `验证了 ${data.components.length} 个组件`, fileName);
  return { componentIds, componentCodes };
}

/**
 * 验证构件数据
 */
function validateAssemblies(data, componentIds) {
  const fileName = 'assemblies.json';
  
  if (!data || !data.assemblies || !Array.isArray(data.assemblies)) {
    addResult('errors', '构件数据结构错误', fileName);
    return {};
  }
  
  const assemblyIds = new Set();
  const assemblyCodes = new Set();
  
  for (const assembly of data.assemblies) {
    // 验证必需字段
    validateRequiredFields(assembly, [
      'id', 'code', 'name', 'assemblyType', 'status'
    ], '构件', fileName);
    
    // 验证ID格式
    validateIdFormat(assembly.id, 'asm_', '构件', fileName);
    
    // 检查ID唯一性
    if (assemblyIds.has(assembly.id)) {
      addResult('errors', `重复的构件ID: ${assembly.id}`, fileName);
    } else {
      assemblyIds.add(assembly.id);
    }
    
    // 检查编码唯一性
    if (assemblyCodes.has(assembly.code)) {
      addResult('errors', `重复的构件编码: ${assembly.code}`, fileName);
    } else {
      assemblyCodes.add(assembly.code);
    }
    
    // 验证构件类型
    const validTypes = ['frame_assembly', 'glass_assembly', 'hardware_assembly', 'complete_assembly'];
    if (!validTypes.includes(assembly.assemblyType)) {
      addResult('warnings', `未知的构件类型: ${assembly.assemblyType}`, fileName);
    }
    
    // 验证组件实例引用
    if (assembly.componentInstances && Array.isArray(assembly.componentInstances)) {
      for (const instance of assembly.componentInstances) {
        if (instance.componentId && !componentIds.has(instance.componentId)) {
          addResult('errors', `构件 ${assembly.id} 引用了不存在的组件: ${instance.componentId}`, fileName);
        }
      }
    }
  }
  
  addResult('info', `验证了 ${data.assemblies.length} 个构件`, fileName);
  return { assemblyIds, assemblyCodes };
}

/**
 * 验证产品数据
 */
function validateProducts(data) {
  const fileName = 'products.json';
  
  if (!data || !data.products || !Array.isArray(data.products)) {
    addResult('errors', '产品数据结构错误', fileName);
    return {};
  }
  
  const productIds = new Set();
  const productCodes = new Set();
  
  for (const product of data.products) {
    // 验证必需字段
    validateRequiredFields(product, [
      'id', 'code', 'name', 'productStructureId', 'category', 'lifecycle', 'status'
    ], '产品', fileName);
    
    // 验证ID格式
    validateIdFormat(product.id, 'prod_', '产品', fileName);
    
    // 检查ID唯一性
    if (productIds.has(product.id)) {
      addResult('errors', `重复的产品ID: ${product.id}`, fileName);
    } else {
      productIds.add(product.id);
    }
    
    // 检查编码唯一性
    if (productCodes.has(product.code)) {
      addResult('errors', `重复的产品编码: ${product.code}`, fileName);
    } else {
      productCodes.add(product.code);
    }
    
    // 验证生命周期
    const validLifecycles = ['design', 'pilot', 'mass_production', 'end_of_life'];
    if (!validLifecycles.includes(product.lifecycle)) {
      addResult('warnings', `未知的产品生命周期: ${product.lifecycle}`, fileName);
    }
  }
  
  addResult('info', `验证了 ${data.products.length} 个产品`, fileName);
  return { productIds, productCodes };
}

/**
 * 验证BOM数据
 */
function validateBOMs(quoteBOMData, productionBOMData, productIds) {
  // 验证报价BOM
  if (quoteBOMData && quoteBOMData.quoteBOMs) {
    const fileName = 'quote-boms.json';
    for (const bom of quoteBOMData.quoteBOMs) {
      validateRequiredFields(bom, [
        'id', 'code', 'name', 'productId', 'status'
      ], '报价BOM', fileName);
      
      validateIdFormat(bom.id, 'qbom_', '报价BOM', fileName);
      
      // 验证产品引用
      if (bom.productId && !productIds.has(bom.productId)) {
        addResult('errors', `报价BOM ${bom.id} 引用了不存在的产品: ${bom.productId}`, fileName);
      }
    }
    addResult('info', `验证了 ${quoteBOMData.quoteBOMs.length} 个报价BOM`, fileName);
  }
  
  // 验证生产BOM
  if (productionBOMData && productionBOMData.productionBOMs) {
    const fileName = 'production-boms.json';
    for (const bom of productionBOMData.productionBOMs) {
      validateRequiredFields(bom, [
        'id', 'code', 'name', 'productId', 'status'
      ], '生产BOM', fileName);
      
      validateIdFormat(bom.id, 'pbom_', '生产BOM', fileName);
      
      // 验证产品引用
      if (bom.productId && !productIds.has(bom.productId)) {
        addResult('errors', `生产BOM ${bom.id} 引用了不存在的产品: ${bom.productId}`, fileName);
      }
    }
    addResult('info', `验证了 ${productionBOMData.productionBOMs.length} 个生产BOM`, fileName);
  }
}

/**
 * 主验证函数
 */
function validateMockData() {
  console.log('🚀 开始验证Mock数据...\n');
  
  // 检查Mock数据目录
  if (!fs.existsSync(MOCK_DATA_DIR)) {
    addResult('errors', `Mock数据目录不存在: ${MOCK_DATA_DIR}`);
    return;
  }
  
  // 加载所有数据文件
  const dataFiles = {};
  for (const fileName of DATA_FILES) {
    const filePath = path.join(MOCK_DATA_DIR, fileName);
    if (fs.existsSync(filePath)) {
      dataFiles[fileName] = validateJsonFormat(filePath);
    } else {
      addResult('warnings', `数据文件不存在: ${fileName}`);
    }
  }
  
  // 验证各类数据
  const { componentIds } = validateComponents(dataFiles['components.json']);
  const { assemblyIds } = validateAssemblies(dataFiles['assemblies.json'], componentIds);
  const { productIds } = validateProducts(dataFiles['products.json']);
  
  validateBOMs(
    dataFiles['quote-boms.json'],
    dataFiles['production-boms.json'],
    productIds
  );
  
  // 输出验证结果
  console.log('\n📊 验证结果统计:');
  console.log(`✅ 信息: ${validationResults.info.length}`);
  console.log(`⚠️  警告: ${validationResults.warnings.length}`);
  console.log(`❌ 错误: ${validationResults.errors.length}`);
  
  if (validationResults.errors.length > 0) {
    console.log('\n❌ 发现错误，需要修复:');
    validationResults.errors.forEach(error => {
      console.log(`  - ${error.file ? `[${error.file}] ` : ''}${error.message}`);
    });
    process.exit(1);
  } else if (validationResults.warnings.length > 0) {
    console.log('\n⚠️  发现警告，建议检查:');
    validationResults.warnings.forEach(warning => {
      console.log(`  - ${warning.file ? `[${warning.file}] ` : ''}${warning.message}`);
    });
  }
  
  console.log('\n🎉 Mock数据验证完成！');
}

// 运行验证
if (import.meta.url === `file://${process.argv[1]}`) {
  validateMockData();
}

export { validateMockData };
