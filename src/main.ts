import './assets/main.css'
import './styles/dialog-scroll-fix.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { setupStore, initializeStores } from './stores'

const app = createApp(App)

// 配置状态管理
setupStore(app)
app.use(router)

// 挂载应用
app.mount('#app')

// 初始化状态管理
initializeStores().catch(error => {
  console.error('状态管理初始化失败:', error)
})

// 开发环境下暴露路由测试工具
if (import.meta.env.DEV) {
  import('./utils/routeTest').then(({ runRouteTests, testCoreRoutes, testAllVisibleRoutes }) => {
    // 暴露到全局对象供控制台调用
    ;(window as any).routeTest = {
      runAll: runRouteTests,
      testCore: testCoreRoutes,
      testAll: testAllVisibleRoutes
    }
    
    console.log('🔧 开发模式: 路由测试工具已加载')
    console.log('💡 使用 window.routeTest.runAll() 运行完整测试')
  })
}
