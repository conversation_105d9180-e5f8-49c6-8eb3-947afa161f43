/**
 * 产品结构管理工具函数
 * 
 * 提供产品结构、组件、构件的操作和验证工具函数
 */

import type {
  ProductStructure,
  Component,
  Assembly,
  ComponentInstance,
  AssemblyInstance,
  ComponentParameter,
  ComponentConstraint,
  ValidationResult,
  ValidationError,
  BOMItem,
  ParameterType
} from '@/types/product-structure';

// ============================================================================
// 参数验证工具函数
// ============================================================================

/**
 * 验证参数值是否符合参数定义
 */
export function validateParameterValue(
  parameter: ComponentParameter,
  value: any
): { isValid: boolean; error?: string } {
  // 必填检查
  if (parameter.required && (value === null || value === undefined || value === '')) {
    return { isValid: false, error: `参数 ${parameter.displayName} 是必填项` };
  }

  // 如果值为空且非必填，则通过验证
  if (!parameter.required && (value === null || value === undefined || value === '')) {
    return { isValid: true };
  }

  // 根据参数类型进行验证
  switch (parameter.type) {
    case 'number':
      if (typeof value !== 'number' || isNaN(value)) {
        return { isValid: false, error: `参数 ${parameter.displayName} 必须是数值` };
      }
      if (parameter.minValue !== undefined && value < parameter.minValue) {
        return { isValid: false, error: `参数 ${parameter.displayName} 不能小于 ${parameter.minValue}` };
      }
      if (parameter.maxValue !== undefined && value > parameter.maxValue) {
        return { isValid: false, error: `参数 ${parameter.displayName} 不能大于 ${parameter.maxValue}` };
      }
      break;

    case 'string':
      if (typeof value !== 'string') {
        return { isValid: false, error: `参数 ${parameter.displayName} 必须是字符串` };
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean') {
        return { isValid: false, error: `参数 ${parameter.displayName} 必须是布尔值` };
      }
      break;

    case 'select':
      if (!parameter.options || !parameter.options.some(opt => opt.value === value)) {
        return { isValid: false, error: `参数 ${parameter.displayName} 的值不在可选范围内` };
      }
      break;

    case 'formula':
      // 公式类型的验证需要更复杂的逻辑，这里简化处理
      if (typeof value !== 'string') {
        return { isValid: false, error: `参数 ${parameter.displayName} 必须是公式字符串` };
      }
      break;
  }

  return { isValid: true };
}

/**
 * 验证多个参数值
 */
export function validateParameterValues(
  parameters: ComponentParameter[],
  values: Record<string, any>
): ValidationResult {
  const errors: ValidationError[] = [];

  parameters.forEach(parameter => {
    const value = values[parameter.name];
    const validation = validateParameterValue(parameter, value);
    
    if (!validation.isValid) {
      errors.push({
        id: `param_${parameter.id}`,
        type: 'parameter_conflict',
        message: validation.error!,
        location: {
          objectType: 'component',
          objectId: parameter.id,
          fieldName: parameter.name
        },
        severity: parameter.required ? 'error' : 'warning',
        suggestions: [`请检查参数 ${parameter.displayName} 的值`]
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    suggestions: [],
    validationTime: new Date().toISOString(),
    summary: {
      totalErrors: errors.length,
      totalWarnings: 0,
      totalSuggestions: 0,
      criticalErrors: errors.filter(e => e.severity === 'error').length
    }
  };
}

// ============================================================================
// 约束验证工具函数
// ============================================================================

/**
 * 简单的表达式求值器（仅支持基本数学运算）
 */
export function evaluateExpression(expression: string, context: Record<string, any>): any {
  try {
    // 替换变量
    let processedExpression = expression;
    Object.keys(context).forEach(key => {
      const regex = new RegExp(`\\b${key}\\b`, 'g');
      processedExpression = processedExpression.replace(regex, String(context[key]));
    });

    // 简单的安全检查，只允许数字、运算符和括号
    if (!/^[\d\s+\-*/().]+$/.test(processedExpression)) {
      throw new Error('表达式包含不安全的字符');
    }

    // 使用 Function 构造器求值（在实际项目中应该使用更安全的表达式求值器）
    return new Function(`return ${processedExpression}`)();
  } catch (error) {
    console.error('表达式求值失败:', expres