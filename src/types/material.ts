// 物料元数据管理相关类型定义

export interface AttributeDefinition {
  name: string;
  type: 'select' | 'number' | 'text';
  options?: string[];
  unit?: string;
  minValue?: number;
  maxValue?: number;
  description?: string;
  // 新增验证相关字段
  required?: boolean; // 是否为必填属性
  defaultValue?: string | number; // 默认值
  placeholder?: string; // 输入提示文本
  validationPattern?: string; // 正则表达式验证模式（仅用于text类型）
}

export interface AttributeSchema {
  baseAttributes: AttributeDefinition[];
  variantAttributes: AttributeDefinition[];
}

export interface MaterialCategory {
  categoryId: string;
  categoryName: string;
  description: string;
  parentId: string | null;
  level: number;
  hasChildren: boolean;
  attributeSchema: AttributeSchema | null;
  // 新增字段用于操作功能
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  materialCount?: number; // 该分类下的物料数量
  isSystemDefault?: boolean; // 是否为系统默认分类（不可删除）
  sortOrder?: number; // 排序权重
}

export interface MaterialVariant {
  variantId: string;
  sku: string;
  displayName: string;
  variantAttributes: Record<string, any>;
  stock: number;
  unit: string;
  cost: number;
  weightKg?: number;
  weightKgPerMeter?: number;
  areaSqm?: number;
  supplier: string;
  leadTimeDays: number;
  isActive: boolean;
}

export interface Material {
  materialId: string;
  categoryId: string;
  displayName: string;
  attributes: Record<string, any>;
  variants: MaterialVariant[];
}

export interface MaterialCategoriesResponse {
  materialCategories: MaterialCategory[];
}

export interface MaterialsResponse {
  materials: Material[];
}

// 树形分类节点（用于组件展示）
export interface MaterialCategoryTreeNode extends MaterialCategory {
  children?: MaterialCategoryTreeNode[];
  expanded?: boolean;
}

export interface MetadataState {
  materialCategories: MaterialCategory[];
  categoryTree: MaterialCategoryTreeNode[];
  materials: Material[];
  selectedCategoryId: string | null;
  selectedMaterialId: string | null;
  expandedCategoryIds: Set<string>;
  loading: boolean;
  error: string | null;
  // 新增操作相关状态
  operationDialogOpen: boolean;
  batchDialogOpen: boolean;
  currentOperation: 'add' | 'edit' | 'delete' | 'export' | 'import' | null;
  editingCategory: MaterialCategory | null;
}

// 操作结果类型
export interface CategoryOperationResult {
  success: boolean;
  message: string;
  data?: MaterialCategory;
  errors?: string[];
}

// 批量操作数据类型
export interface BatchOperationData {
  exportData?: {
    categoryIds: string[];
    format: 'json' | 'excel' | 'template';
  };
  deleteData?: {
    categoryIds: string[];
  };
  importData?: {
    mode: 'file' | 'template';
    file?: File;
    templateId?: string;
  };
}

// 数据验证结果类型
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}