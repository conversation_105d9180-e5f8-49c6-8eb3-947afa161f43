/**
 * 类型定义统一导出文件
 * 
 * 统一管理所有业务类型定义，确保类型系统的一致性和可维护性
 */

// 导出现有的产品配置相关类型
export * from './product';

// 导出新的产品结构管理相关类型
export * from './product-structure';

// 类型兼容性映射
// 为了保持向后兼容，提供一些类型别名

import type { 
  ProductStructure as NewProductStructure,
  Component as StructureComponent,
  Assembly as StructureAssembly,
  ComponentParameter as StructureParameter,
  ComponentConstraint as StructureConstraint,
  ValidationResult as StructureValidationResult
} from './product-structure';

import type {
  ProductConfiguration,
  ProductStructureTemplate,
  ComponentDefinition,
  MaterialComponent
} from './product';

// 类型别名，用于区分不同层次的产品概念
export type {
  // 产品结构管理层面（设计阶段）
  NewProductStructure as ProductStructureDesign,
  StructureComponent as DesignComponent,
  StructureAssembly as DesignAssembly,
  StructureParameter as DesignParameter,
  StructureConstraint as DesignConstraint,
  StructureValidationResult as DesignValidationResult,
  
  // 产品配置层面（生产阶段）
  ProductConfiguration as ProductionConfiguration,
  ProductStructureTemplate as ProductionTemplate,
  ComponentDefinition as ProductionComponent,
  MaterialComponent as ProductionMaterial
};

// 通用工具类型
export type ID = string;
export type Timestamp = string;
export type Status = 'draft' | 'active' | 'deprecated' | 'archived';

// 分页相关类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// API响应包装类型
export interface ApiResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  timestamp: string;
}

// 表单验证相关类型
export interface FormValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'boolean' | 'date' | 'textarea';
  rules?: FormValidationRule[];
  options?: Array<{ label: string; value: any }>;
  placeholder?: string;
  disabled?: boolean;
  visible?: boolean;
}

// 搜索和筛选通用类型
export interface SearchParams {
  keyword?: string;
  filters?: Record<string, any>;
  pagination?: PaginationParams;
}

export interface FilterOption {
  label: string;
  value: any;
  count?: number;
}

export interface FilterGroup {
  name: string;
  label: string;
  type: 'select' | 'multiselect' | 'range' | 'date';
  options?: FilterOption[];
  min?: number;
  max?: number;
}

// 导出导入相关类型
export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf' | 'json';
  fields?: string[];
  filters?: Record<string, any>;
  includeHeaders?: boolean;
}

export interface ImportResult {
  success: boolean;
  totalRows: number;
  successRows: number;
  errorRows: number;
  errors?: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

// 审计日志相关类型
export interface AuditLog {
  id: string;
  entityType: string;
  entityId: string;
  action: 'create' | 'update' | 'delete' | 'view';
  changes?: Record<string, { before: any; after: any }>;
  userId: string;
  userName: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}

// 权限相关类型
export interface Permission {
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete';
  conditions?: Record<string, any>;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
}

export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  roles: Role[];
  status: 'active' | 'inactive' | 'locked';
  lastLoginAt?: string;
}

// 系统配置相关类型
export interface SystemConfig {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json';
  description: string;
  category: string;
  editable: boolean;
}

// 通知相关类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: Array<{
    label: string;
    action: string;
    primary?: boolean;
  }>;
}

// 文件上传相关类型
export interface FileUpload {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
  uploadedAt: string;
  uploadedBy: string;
}

// 工作流相关类型
export interface WorkflowStep {
  id: string;
  name: string;
  type: 'approval' | 'notification' | 'automation';
  assignee?: string;
  status: 'pending' | 'completed' | 'skipped' | 'rejected';
  completedAt?: string;
  comments?: string;
}

export interface WorkflowInstance {
  id: string;
  workflowType: string;
  entityType: string;
  entityId: string;
  status: 'running' | 'completed' | 'cancelled' | 'failed';
  currentStep?: string;
  steps: WorkflowStep[];
  startedAt: string;
  completedAt?: string;
  startedBy: string;
}