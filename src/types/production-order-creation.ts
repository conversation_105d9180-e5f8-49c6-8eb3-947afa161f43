/**
 * 生产工单创建相关类型定义
 */

import type { CustomerOrder, CustomerOrderItem, ProcessStep } from './mes-validation'

// 产品规格接口（从mes-validation中复制）
export interface ProductSpecifications {
  length: number
  width: number
  thickness: number
  glassType: 'clear' | 'tinted' | 'low_e' | 'reflective'
  color?: string
}

// ============ 基础数据类型 ============

/**
 * 选中的订单项
 */
export interface SelectedOrderItem {
  id: string
  customerOrderId: string
  orderNumber: string
  customerName: string
  specifications: ProductSpecifications
  totalQuantity: number
  selectedQuantity: number
  processFlow: ProcessStep[]
  deliveryDate: string
  originalItem: CustomerOrderItem
}

/**
 * 批次优化结果
 */
export interface BatchOptimizationResult {
  efficiency: number // 效率提升百分比
  timeSaved: number // 节省工时（小时）
  batches: OptimizedBatch[]
  recommendations: string[]
  totalItems: number
  totalQuantity: number
}

/**
 * 优化后的批次
 */
export interface OptimizedBatch {
  id: string
  name: string
  items: SelectedOrderItem[]
  processFlow: ProcessStep[]
  workstation: string
  workstationGroup: string
  totalQuantity: number
  estimatedTime: number // 分钟
  utilization: number // 设备利用率百分比
  priority: 'urgent' | 'high' | 'normal' | 'low'
  conflictLevel: 'none' | 'minor' | 'major'
}

/**
 * 工艺冲突
 */
export interface ProcessConflict {
  id: string
  conflictType: 'parameter' | 'sequence' | 'equipment' | 'material'
  severity: 'warning' | 'error'
  affectedItems: string[]
  description: string
  suggestions: string[]
  autoResolvable: boolean
}

/**
 * 验证结果
 */
export interface ValidationResult {
  id: string
  level: 'info' | 'warning' | 'error'
  category: 'process' | 'schedule' | 'resource' | 'business'
  title: string
  message: string
  suggestion?: string
  affectedItems?: string[]
  autoFixable?: boolean
}

/**
 * 批次配置
 */
export interface BatchConfiguration {
  batchId: string
  customProcessFlow?: ProcessStep[]
  priorityOverride?: 'urgent' | 'high' | 'normal' | 'low'
  schedulingConstraints?: SchedulingConstraint[]
  notes?: string
}

/**
 * 调度约束
 */
export interface SchedulingConstraint {
  type: 'before' | 'after' | 'parallel' | 'sequential'
  targetBatchId?: string
  targetDate?: string
  reason: string
}

// ============ 状态管理类型 ============

/**
 * 工单创建状态
 */
export interface WorkOrderCreationState {
  // 基础数据
  availableOrders: CustomerOrder[]
  selectedOrderItems: SelectedOrderItem[]
  
  // 批次优化
  batchOptimization: BatchOptimizationResult | null
  processConflicts: ProcessConflict[]
  
  // 配置参数
  workOrderPriority: 'urgent' | 'high' | 'normal' | 'low'
  plannedStartDate: string
  customBatchConfig: Record<string, BatchConfiguration>
  
  // 智能推荐
  scheduleRecommendation: string
  estimatedEndDate: string
  validationResults: ValidationResult[]
  
  // 界面状态
  isLoading: boolean
  isCreating: boolean
  showConflictDetails: boolean
  showBatchDetails: boolean
  activeTab: 'selection' | 'optimization' | 'validation'
  errors: Record<string, string>
  
  // 搜索和筛选
  searchQuery: string
  statusFilter: string
  processTypeFilter: string
  customerFilter: string
}

/**
 * 工艺流程推荐
 */
export interface ProcessFlowRecommendation {
  orderItemId: string
  recommendedFlow: ProcessStep[]
  confidence: number // 0-1
  alternatives: ProcessStep[][]
  reasoning: string[]
  estimatedTime: number
  resourceRequirements: ResourceRequirement[]
}

/**
 * 资源需求
 */
export interface ResourceRequirement {
  resourceType: 'equipment' | 'material' | 'labor'
  resourceId: string
  resourceName: string
  quantity: number
  duration: number // 分钟
  availability: 'available' | 'limited' | 'unavailable'
}

/**
 * 计划推荐
 */
export interface ScheduleRecommendation {
  recommendedStartDate: string
  estimatedEndDate: string
  confidence: number
  reasoning: string[]
  risks: ScheduleRisk[]
  alternatives: AlternativeSchedule[]
}

/**
 * 计划风险
 */
export interface ScheduleRisk {
  type: 'capacity' | 'material' | 'delivery' | 'quality'
  severity: 'low' | 'medium' | 'high'
  description: string
  mitigation: string
  probability: number // 0-1
}

/**
 * 备选计划
 */
export interface AlternativeSchedule {
  startDate: string
  endDate: string
  advantages: string[]
  disadvantages: string[]
  riskLevel: 'low' | 'medium' | 'high'
}

// ============ 服务接口类型 ============

/**
 * 智能推荐服务接口
 */
export interface SmartRecommendationService {
  // 工艺流程推荐
  generateProcessFlow(orderItem: CustomerOrderItem): Promise<ProcessFlowRecommendation>
  
  // 批次优化
  optimizeBatches(orderItems: SelectedOrderItem[]): Promise<BatchOptimizationResult>
  
  // 计划时间推荐
  recommendSchedule(
    batches: OptimizedBatch[], 
    priority: string,
    constraints?: SchedulingConstraint[]
  ): Promise<ScheduleRecommendation>
  
  // 实时验证
  validateWorkOrderConfig(state: WorkOrderCreationState): Promise<ValidationResult[]>
  
  // 冲突检测
  detectProcessConflicts(orderItems: SelectedOrderItem[]): Promise<ProcessConflict[]>
}

/**
 * 能力检查结果（预留接口）
 */
export interface CapacityCheckResult {
  available: boolean
  utilization: number
  bottlenecks: Bottleneck[]
  recommendations: string[]
  alternativeTimeSlots: TimeSlot[]
}

/**
 * 瓶颈信息
 */
export interface Bottleneck {
  resourceId: string
  resourceName: string
  currentLoad: number
  maxCapacity: number
  conflictingOrders: string[]
}

/**
 * 时间段
 */
export interface TimeSlot {
  startTime: string
  endTime: string
  availability: number // 0-1
  suitability: number // 0-1
}

// ============ 组件Props和Events类型 ============

/**
 * 订单项选择面板Props
 */
export interface OrderItemSelectionPanelProps {
  availableOrders: CustomerOrder[]
  selectedOrderItems: SelectedOrderItem[]
  searchQuery: string
  statusFilter: string
  processTypeFilter: string
  loading: boolean
}

/**
 * 订单项选择面板Events
 */
export interface OrderItemSelectionPanelEvents {
  (e: 'order-item-selected', item: CustomerOrderItem, quantity: number): void
  (e: 'order-item-removed', itemId: string): void
  (e: 'quantity-changed', itemId: string, quantity: number): void
  (e: 'search-changed', query: string): void
  (e: 'filter-changed', filterType: string, value: string): void
}

/**
 * 批次优化面板Props
 */
export interface BatchOptimizationPanelProps {
  selectedOrderItems: SelectedOrderItem[]
  batchOptimization: BatchOptimizationResult | null
  processConflicts: ProcessConflict[]
  validationResults: ValidationResult[]
  loading: boolean
}

/**
 * 批次优化面板Events
 */
export interface BatchOptimizationPanelEvents {
  (e: 'batch-modified', batchId: string, config: BatchConfiguration): void
  (e: 'conflict-resolved', conflictId: string, resolution: string): void
  (e: 'optimization-requested'): void
  (e: 'batch-details-requested', batchId: string): void
}

/**
 * 操作控制面板Props
 */
export interface ActionControlPanelProps {
  canCreate: boolean
  isCreating: boolean
  validationSummary: ValidationSummary
  selectedItemsCount: number
  estimatedBatches: number
}

/**
 * 操作控制面板Events
 */
export interface ActionControlPanelEvents {
  (e: 'create-order'): void
  (e: 'save-draft'): void
  (e: 'cancel'): void
  (e: 'preview-order'): void
}

/**
 * 验证摘要
 */
export interface ValidationSummary {
  totalIssues: number
  errors: number
  warnings: number
  infos: number
  canProceed: boolean
  criticalIssues: ValidationResult[]
}

// ============ 工具类型 ============

/**
 * 创建工单请求
 */
export interface CreateWorkOrderRequest {
  batches: OptimizedBatch[]
  priority: 'urgent' | 'high' | 'normal' | 'low'
  plannedStartDate: string
  customConfigurations: Record<string, BatchConfiguration>
  notes?: string
  createdBy: string
}

/**
 * 创建工单响应
 */
export interface CreateWorkOrderResponse {
  success: boolean
  workOrderIds: string[]
  errors?: string[]
  warnings?: string[]
}

/**
 * 草稿保存数据
 */
export interface WorkOrderDraft {
  id: string
  name: string
  selectedOrderItems: SelectedOrderItem[]
  batchOptimization: BatchOptimizationResult | null
  configurations: Record<string, BatchConfiguration>
  createdAt: string
  updatedAt: string
}