// 玻璃产品配置系统类型定义

// 基础类型
export interface VersionedEntity {
  id: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

// 状态枚举
export enum LifecycleStatus {
  DRAFT = 'draft',           // 草稿
  ACTIVE = 'active',         // 激活
  DEPRECATED = 'deprecated', // 已弃用
  ARCHIVED = 'archived'      // 已归档
}

// 产品结构模板
export interface ProductStructureTemplate extends VersionedEntity {
  name: string;                    // 如：中空玻璃、夹胶玻璃
  description: string;
  components: ComponentDefinition[];
  status: LifecycleStatus;
}

export interface ComponentDefinition {
  id: string;
  name: string;                    // 如：面玻、隔条、背玻
  materialType: string;            // 材料类型
  quantityFormula: string;         // 如：'1'、'2*length+2*width'、'length*width'
  unit: string;                    // 单位：片、米、㎡
  required: boolean;
}

// 产品配置
export interface ProductConfiguration extends VersionedEntity {
  name: string;                    // 如：中空5#白玻+12a+5#lowe
  description: string;
  structureTemplateId: string;     // 关联的结构模板
  structureTemplateName: string;   // 结构模板名称（冗余字段）
  materialComponents: MaterialComponent[];
  status: LifecycleStatus;
  
  // 统计信息
  usageCount: number;              // 使用次数
  lastUsedAt?: string;             // 最后使用时间
  averageCost: number;             // 平均成本
  
  // 标签和分类
  tags: string[];                  // 标签
  category: string;                // 分类
  subCategory: string;             // 子分类
}

export interface MaterialComponent {
  id: string;
  componentId: string;             // 对应ComponentDefinition的ID
  componentName: string;           // 组件名称
  materialVariantId: string;       // 具体材料变体
  materialVariantName: string;     // 如：5#白玻、12a铝隔条
  quantityFormula: string;         // 继承或覆盖模板公式
  wastageRate: number;             // 损耗率 0.05 = 5%
  unitCost: number;                // 单位成本
  unit: string;                    // 单位
}

// 客户订单
export interface CustomerOrder extends VersionedEntity {
  orderNumber: string;
  customerName: string;
  orderDate: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'draft' | 'quoted' | 'confirmed' | 'production';
}

export interface OrderItem {
  id: string;
  productDescription: string;      // 客户描述：中空5#白玻+12a+5#lowe
  productConfigurationId?: string; // 匹配的产品配置ID
  specifications: OrderSpecification[];
  totalQuantity: number;
  unitPrice: number;
  totalPrice: number;
  needsConfiguration: boolean;     // 是否需要工艺工程师配置
}

export interface OrderSpecification {
  length: number;                  // 长度 mm
  width: number;                   // 宽度 mm
  quantity: number;                // 数量
  area: number;                    // 面积 ㎡ (自动计算)
}

// 材料需求
export interface MaterialRequirement {
  id: string;
  orderItemId: string;
  materialVariantId: string;
  materialVariantName: string;
  requiredQuantity: number;        // 所需数量
  unit: string;
  unitCost: number;
  totalCost: number;
  calculationDetails: CalculationDetail[];
}

export interface CalculationDetail {
  specificationIndex: number;      // 对应OrderSpecification的索引
  length: number;
  width: number;
  quantity: number;
  calculatedAmount: number;        // 单个规格的用量
  formula: string;                 // 使用的计算公式
}

// 产品匹配结果
export interface ProductMatchResult {
  match?: ProductConfiguration;
  confidence: number;
  alternatives?: ProductConfiguration[];
  needsConfiguration: boolean;
  configurationRequestId?: string;
}

// 成本分解
export interface OrderCostBreakdown {
  materialCost: number;
  processingCost: number;
  overheadCost: number;
  baseCost: number;
  profit: number;
  finalPrice: number;
  profitMargin: number;
}

// 搜索和筛选
export interface ProductSearchFilters {
  keyword?: string;
  status?: LifecycleStatus[];
  structureTemplate?: string[];
  category?: string[];
  tags?: string[];
  usageCountRange?: [number, number];
  costRange?: [number, number];
  dateRange?: [string, string];
}

// 产品统计
export interface ProductUsageStatistics {
  productConfigurationId: string;
  productName: string;
  totalUsage: number;
  monthlyUsage: number;
  averageCost: number;
  lastUsedAt: string;
  trendDirection: 'up' | 'down' | 'stable';
}

// 配置请求
export interface ConfigurationRequest extends VersionedEntity {
  productDescription: string;
  customerName: string;
  orderNumber: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'rejected';
  assignedTo?: string;             // 分配给的工艺工程师
  estimatedCompletionTime?: string;
  actualCompletionTime?: string;
  notes?: string;
}