/**
 * 物料变体管理相关的TypeScript类型定义
 * 基于玻璃深加工企业的物料变体管理需求
 */

// 属性值接口
export interface AttributeValue {
  attributeId: string
  attributeName: string
  value: string | number
  unit?: string
}

// 基础属性配置
export interface BaseAttribute {
  id: string
  name: string // "厚度"、"颜色"、"等级"
  type: 'number' | 'text' | 'select'
  unit?: string // "mm"
  options?: string[] // ["透明", "茶色", "蓝色"]
  isRequired: boolean
  description?: string
}

// 变体属性配置
export interface VariantAttribute {
  id: string
  name: string // "宽度"、"高度"、"长度"
  type: 'number'
  unit: string // "mm"
  minValue?: number
  maxValue?: number
  isRequired: boolean
  description?: string
}

// 物料模板接口
export interface MaterialTemplate {
  id: string
  name: string // "浮法玻璃"
  code: string // "GLASS_FLOAT"
  category: string
  materialType: 'raw_glass' | 'profile' | 'hardware' | 'sealant' | 'chemical'
  baseAttributes: BaseAttribute[] // 基础规格：厚度、颜色、等级
  variantAttributes: VariantAttribute[] // 变体规格：宽度、高度、长度
  isActive: boolean
  createdAt: string
  updatedAt?: string
}

// 供应商接口
export interface Supplier {
  id: string
  name: string
  code: string
  contactPerson: string
  phone: string
  email: string
  address: string
  isActive: boolean
}

// 物料变体接口
export interface MaterialVariant {
  id: string
  templateId: string
  sku: string // "GLASS_FLOAT_6MM_CLEAR_3300x2140"
  displayName: string // "6mm透明浮法玻璃 3300x2140mm"
  baseAttributeValues: AttributeValue[] // 厚度:6mm, 颜色:透明, 等级:优等品
  variantAttributeValues: AttributeValue[] // 宽度:3300mm, 高度:2140mm
  cost: number
  weight: number
  area?: number // 对于玻璃，面积是重要属性
  supplier: Supplier
  leadTime: number
  isActive: boolean
  stockQuantity: number
  reservedQuantity: number
  availableQuantity: number
  createdAt: string
  updatedAt?: string
}

// 玻璃原片物料变体
export interface GlassSheetVariant extends MaterialVariant {
  thickness: number // mm
  color: string // "透明"、"茶色"、"蓝色"
  grade: string // "优等品"、"一等品"
  width: number // mm
  height: number // mm
  glassType: 'float' | 'ultra_clear' | 'tinted' | 'reflective'
  surfaceQuality: 'standard' | 'premium'
}

// 型材物料变体
export interface ProfileVariant extends MaterialVariant {
  crossSection: string // "50x30mm"
  material: string // "铝合金"、"塑钢"
  color: string // "银白色"、"香槟色"
  length: number // mm
  wallThickness: number // mm
  surfaceTreatment: 'anodized' | 'powder_coated' | 'electrophoresis'
}

// 库存位置
export interface StockLocation {
  id: string
  name: string
  code: string
  type: 'warehouse' | 'workshop' | 'staging'
  isActive: boolean
}

// 物料变体库存
export interface MaterialVariantStock {
  id: string
  materialVariantId: string
  materialVariant: MaterialVariant
  locationId: string
  location: StockLocation
  quantity: number
  reservedQuantity: number
  availableQuantity: number
  unitCost: number
  totalValue: number
  lastMovementDate: string
  reorderPoint: number // 物料变体级别的安全库存
  maxStock: number // 物料变体级别的最大库存
  lotNumbers: string[] // 批次号
  expiryDate?: string // 过期日期（如适用）
}

// 物料变体库存移动
export interface MaterialVariantStockMove {
  id: string
  materialVariantId: string
  materialVariant: MaterialVariant
  quantity: number
  sourceLocationId: string
  destinationLocationId: string
  moveType: 'receipt' | 'delivery' | 'internal' | 'adjustment' | 'scrap'
  reference: string
  state: 'draft' | 'confirmed' | 'done' | 'cancelled'
  unitCost: number
  totalCost: number
  scheduledDate: string
  effectiveDate?: string
  relatedOrderId?: string
  cuttingPlanId?: string // 关联的切割计划
}

// 余料物料变体库存
export interface WasteMaterialVariantStock {
  id: string
  originalMaterialVariantId: string
  originalMaterialVariant: MaterialVariant
  currentDimensions: Dimensions
  remainingArea: number // 对于玻璃
  remainingLength: number // 对于型材
  quality: 'good' | 'damaged' | 'unusable'
  locationId: string
  createdDate: string
  lastUsedDate?: string
  potentialUses: PotentialUse[] // 可能的用途匹配
}

// 尺寸接口
export interface Dimensions {
  width?: number
  height?: number
  length?: number
  thickness?: number
}

// 潜在用途
export interface PotentialUse {
  orderItemId: string
  requiredDimensions: Dimensions
  matchScore: number // 匹配度评分
  wasteAfterUse: number
}

// 物料变体补货规则
export interface MaterialVariantReorderRule {
  id: string
  materialVariantId: string
  materialVariant: MaterialVariant
  minQuantity: number
  maxQuantity: number
  reorderQuantity: number
  leadTime: number
  supplierId: string
  isActive: boolean
  seasonalAdjustment?: SeasonalAdjustment[]
}

// 季节性调整
export interface SeasonalAdjustment {
  month: number
  adjustmentFactor: number // 季节性调整系数
}

// 物料变体库存预警
export interface MaterialVariantStockAlert {
  id: string
  materialVariantId: string
  materialVariant: MaterialVariant
  alertType: 'low_stock' | 'overstock' | 'no_stock' | 'expiring' | 'slow_moving'
  currentQuantity: number
  thresholdQuantity: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  createdDate: string
  isResolved: boolean
  resolvedDate?: string
}

// 物料变体选择器过滤条件
export interface MaterialVariantFilter {
  templateId?: string
  materialType?: string[]
  baseAttributes?: Record<string, any>
  variantAttributes?: Record<string, any>
  stockStatus?: 'in_stock' | 'low_stock' | 'out_of_stock'
  isActive?: boolean
  supplierId?: string
  priceRange?: {
    min: number
    max: number
  }
}

// 物料变体搜索结果
export interface MaterialVariantSearchResult {
  variants: MaterialVariant[]
  totalCount: number
  filters: MaterialVariantFilter
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

// 物料变体操作结果
export interface MaterialVariantOperationResult {
  success: boolean
  message: string
  data?: MaterialVariant | MaterialVariant[]
  errors?: string[]
}

// 物料变体状态管理接口
export interface MaterialVariantState {
  // 物料模板
  materialTemplates: MaterialTemplate[]
  selectedTemplateId: string | null
  
  // 物料变体
  materialVariants: MaterialVariant[]
  selectedVariantId: string | null
  
  // 库存管理
  variantStocks: MaterialVariantStock[]
  stockMoves: MaterialVariantStockMove[]
  wasteStocks: WasteMaterialVariantStock[]
  stockAlerts: MaterialVariantStockAlert[]
  
  // 补货管理
  reorderRules: MaterialVariantReorderRule[]
  
  // 供应商
  suppliers: Supplier[]
  
  // 库存位置
  stockLocations: StockLocation[]
  
  // 加载状态
  isLoadingTemplates: boolean
  isLoadingVariants: boolean
  isLoadingStocks: boolean
  
  // 错误状态
  templatesError: string | null
  variantsError: string | null
  stocksError: string | null
  
  // 搜索和过滤
  searchQuery: string
  currentFilter: MaterialVariantFilter
  searchResults: MaterialVariantSearchResult | null
}