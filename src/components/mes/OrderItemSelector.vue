<template>
  <div class="border rounded-lg bg-white shadow-sm order-selector-container">
    <!-- 订单头部 -->
    <div class="p-3 bg-gray-50 border-b rounded-t-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <!-- 全选复选框 -->
          <Checkbox 
            :checked="isOrderFullySelected"
            :indeterminate="isOrderPartiallySelected"
            @update:checked="handleOrderSelectAll"
            :disabled="!hasAvailableItems"
          />
          <div>
            <div class="font-medium text-sm">{{ order.orderNumber }}</div>
            <div class="text-xs text-gray-600">{{ order.customerName }}</div>
          </div>
        </div>
        <div class="text-right">
          <Badge :variant="getOrderStatusVariant(order.status)" class="text-xs">
            {{ getOrderStatusText(order.status) }}
          </Badge>
          <div class="text-xs text-gray-500 mt-1">
            {{ formatDate((order as any).deliveryDate) }}
          </div>
        </div>
      </div>
      
      <!-- 订单统计 -->
      <div class="mt-2 flex items-center gap-4 text-xs text-gray-600">
        <span class="flex items-center gap-1">
          <Package class="w-3 h-3" />
          {{ order.items.length }}个产品
        </span>
        <span class="flex items-center gap-1">
          <Users class="w-3 h-3" />
          {{ order.customerName }}
        </span>
        <span v-if="selectedItemsCount > 0" class="flex items-center gap-1 text-blue-600">
          <CheckCircle class="w-3 h-3" />
          已选 {{ selectedItemsCount }}/{{ order.items.length }}
        </span>
      </div>
    </div>
    
    <!-- 订单项列表 -->
    <div class="divide-y order-items-list">
      <div 
        v-for="item in order.items" 
        :key="item.id"
        class="p-3 hover:bg-gray-50 transition-colors"
        :class="{
          'bg-blue-50 border-l-2 border-l-blue-500': isOrderItemSelected(item.id),
          'opacity-50': !isOrderItemAvailable(item)
        }"
      >
        <div class="flex items-center gap-3">
          <!-- 选择框 -->
          <Checkbox 
            :checked="isOrderItemSelected(item.id)"
            @update:checked="(checked) => handleOrderItemToggle(item, checked)"
            :disabled="!isOrderItemAvailable(item)"
          />
          
          <!-- 产品信息 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-1">
              <div class="font-medium text-sm">
                {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
              </div>
              <Badge variant="outline" class="text-xs">
                {{ item.specifications.glassType }}
              </Badge>
              <Badge variant="secondary" class="text-xs">
                {{ item.specifications.color }}
              </Badge>
            </div>
            
            <div class="flex items-center gap-4 text-xs text-gray-500">
              <span class="flex items-center gap-1">
                <Package class="w-3 h-3" />
                总量: {{ item.quantity }}片
              </span>
              <span class="flex items-center gap-1">
                <Settings class="w-3 h-3" />
                {{ getProcessSummary((item as any).processFlow) }}
              </span>
              <span v-if="getAvailableQuantity(item.id) < item.quantity" class="text-yellow-600">
                可用: {{ getAvailableQuantity(item.id) }}片
              </span>
            </div>
            
            <!-- 工艺兼容性指示 -->
            <div v-if="isOrderItemSelected(item.id)" class="mt-2">
              <div v-if="getProcessCompatibility(item.id) === 'compatible'" 
                   class="flex items-center gap-1 text-xs text-green-600">
                <CheckCircle class="w-3 h-3" />
                工艺兼容
              </div>
              <div v-else-if="getProcessCompatibility(item.id) === 'warning'" 
                   class="flex items-center gap-1 text-xs text-yellow-600">
                <AlertTriangle class="w-3 h-3" />
                工艺差异
              </div>
              <div v-else-if="getProcessCompatibility(item.id) === 'conflict'" 
                   class="flex items-center gap-1 text-xs text-red-600">
                <XCircle class="w-3 h-3" />
                工艺冲突
              </div>
            </div>
          </div>
          
          <!-- 数量调整控件 -->
          <div v-if="isOrderItemSelected(item.id)" class="flex items-center gap-2">
            <div class="flex items-center border rounded-md">
              <Button
                size="sm"
                variant="ghost"
                class="h-7 w-7 p-0 rounded-r-none"
                @click="decreaseQuantity(item.id)"
                :disabled="getSelectedQuantity(item.id) <= 1"
              >
                <Minus class="w-3 h-3" />
              </Button>
              
              <Input 
                type="number" 
                :model-value="getSelectedQuantity(item.id)"
                @update:model-value="(value) => handleQuantityChange(item.id, value)"
                @blur="(e: Event) => handleQuantityBlur(item.id, e)"
                :max="getAvailableQuantity(item.id)"
                :min="1"
                class="w-16 h-7 text-xs text-center border-0 border-x rounded-none focus:ring-0"
              />
              
              <Button
                size="sm"
                variant="ghost"
                class="h-7 w-7 p-0 rounded-l-none"
                @click="increaseQuantity(item.id)"
                :disabled="getSelectedQuantity(item.id) >= getAvailableQuantity(item.id)"
              >
                <Plus class="w-3 h-3" />
              </Button>
            </div>
            
            <div class="text-xs text-gray-500">
              / {{ getAvailableQuantity(item.id) }}
            </div>
          </div>
          
          <!-- 快速选择按钮 -->
          <div v-else-if="isOrderItemAvailable(item)" class="flex items-center gap-1">
            <Button
              size="sm"
              variant="outline"
              class="h-6 px-2 text-xs"
              @click="quickSelect(item, Math.min(item.quantity, 50))"
            >
              选择50
            </Button>
            <Button
              size="sm"
              variant="outline"
              class="h-6 px-2 text-xs"
              @click="quickSelect(item, getAvailableQuantity(item.id))"
            >
              全选
            </Button>
          </div>
          
          <!-- 不可用提示 -->
          <div v-else class="flex items-center gap-2">
            <div class="text-xs text-red-500 flex items-center gap-1">
              <XCircle class="w-3 h-3" />
              {{ getUnavailableReason(item) }}
            </div>
            <Button 
              v-if="getUnavailableReason(item).includes('工艺流程')"
              size="sm" 
              variant="ghost" 
              class="h-5 px-2 text-xs text-blue-600 hover:text-blue-700"
              @click="$emit('configure-process', item)"
            >
              配置工艺
            </Button>
          </div>
        </div>
        
        <!-- 批量操作提示 -->
        <div v-if="isOrderItemSelected(item.id) && showBatchHint(item)" 
             class="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
          <div class="flex items-center gap-1 text-blue-700">
            <Lightbulb class="w-3 h-3" />
            批次建议
          </div>
          <div class="text-blue-600 mt-1">
            {{ getBatchHint(item) }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 订单操作栏 -->
    <div v-if="selectedItemsCount > 0" class="p-3 bg-gray-50 border-t rounded-b-lg">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          已选择 <span class="font-medium text-blue-600">{{ selectedItemsCount }}</span> 个产品，
          共 <span class="font-medium text-blue-600">{{ selectedTotalQuantity }}</span> 片
        </div>
        <div class="flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            @click="clearOrderSelections"
            class="text-xs"
          >
            <X class="w-3 h-3 mr-1" />
            清空
          </Button>
          <Button
            size="sm"
            variant="outline"
            @click="optimizeSelections"
            class="text-xs"
          >
            <Zap class="w-3 h-3 mr-1" />
            优化
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Package,
  Settings,
  Plus,
  Minus,
  X,
  Users,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Lightbulb,
  Zap,
} from 'lucide-vue-next'

import type { CustomerOrder, CustomerOrderItem } from '@/types/mes-validation'
import type { SelectedOrderItem } from '@/types/production-order-creation'

// Props
interface Props {
  order: CustomerOrder
  selectedOrderItems: SelectedOrderItem[]
  conflictingItems?: string[]
  unavailableItems?: Record<string, string>
}

// Events
interface Emits {
  (e: 'order-item-selected', item: CustomerOrderItem, quantity: number): void
  (e: 'order-item-removed', itemId: string): void
  (e: 'quantity-changed', itemId: string, quantity: number): void
  (e: 'batch-optimization-requested', orderItems: CustomerOrderItem[]): void
  (e: 'configure-process', item: CustomerOrderItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const selectedItemsCount = computed(() => {
  return props.order.items.filter(item => isOrderItemSelected(item.id)).length
})

const selectedTotalQuantity = computed(() => {
  return props.order.items
    .filter(item => isOrderItemSelected(item.id))
    .reduce((sum, item) => sum + getSelectedQuantity(item.id), 0)
})

const isOrderFullySelected = computed(() => {
  const availableItems = props.order.items.filter(item => isOrderItemAvailable(item))
  return availableItems.length > 0 && availableItems.every(item => isOrderItemSelected(item.id))
})

const isOrderPartiallySelected = computed(() => {
  const selectedCount = selectedItemsCount.value
  return selectedCount > 0 && selectedCount < props.order.items.filter(item => isOrderItemAvailable(item)).length
})

const hasAvailableItems = computed(() => {
  return props.order.items.some(item => isOrderItemAvailable(item))
})

// 事件处理
const handleOrderSelectAll = (checked: boolean) => {
  const availableItems = props.order.items.filter(item => isOrderItemAvailable(item))
  
  if (checked) {
    // 全选
    availableItems.forEach(item => {
      if (!isOrderItemSelected(item.id)) {
        emit('order-item-selected', item, getAvailableQuantity(item.id))
      }
    })
  } else {
    // 取消全选
    availableItems.forEach(item => {
      if (isOrderItemSelected(item.id)) {
        emit('order-item-removed', item.id)
      }
    })
  }
}

const handleOrderItemToggle = (item: CustomerOrderItem, checked: boolean) => {
  if (checked) {
    const defaultQuantity = Math.min(item.quantity, 100) // 默认选择100片或全部
    emit('order-item-selected', item, defaultQuantity)
  } else {
    emit('order-item-removed', item.id)
  }
}

const handleQuantityChange = (itemId: string, value: string | number) => {
  const quantity = typeof value === 'string' ? parseInt(value) || 1 : value
  const maxQuantity = getAvailableQuantity(itemId)
  const validQuantity = Math.max(1, Math.min(quantity, maxQuantity))
  
  emit('quantity-changed', itemId, validQuantity)
}

const handleQuantityInput = (itemId: string, event: Event) => {
  const target = event.target as HTMLInputElement
  const quantity = parseInt(target.value) || 1
  handleQuantityChange(itemId, quantity)
}

const handleQuantityBlur = (itemId: string, event: Event) => {
  const target = event.target as HTMLInputElement
  const quantity = parseInt(target.value) || 1
  const maxQuantity = getAvailableQuantity(itemId)
  const validQuantity = Math.max(1, Math.min(quantity, maxQuantity))
  
  target.value = validQuantity.toString()
  emit('quantity-changed', itemId, validQuantity)
}

const increaseQuantity = (itemId: string) => {
  const currentQuantity = getSelectedQuantity(itemId)
  const maxQuantity = getAvailableQuantity(itemId)
  if (currentQuantity < maxQuantity) {
    emit('quantity-changed', itemId, currentQuantity + 1)
  }
}

const decreaseQuantity = (itemId: string) => {
  const currentQuantity = getSelectedQuantity(itemId)
  if (currentQuantity > 1) {
    emit('quantity-changed', itemId, currentQuantity - 1)
  }
}

const quickSelect = (item: CustomerOrderItem, quantity: number) => {
  emit('order-item-selected', item, quantity)
}

const clearOrderSelections = () => {
  props.order.items.forEach(item => {
    if (isOrderItemSelected(item.id)) {
      emit('order-item-removed', item.id)
    }
  })
}

const optimizeSelections = () => {
  const selectedItems = props.order.items.filter(item => isOrderItemSelected(item.id))
  emit('batch-optimization-requested', selectedItems)
}

// 工具方法
const isOrderItemSelected = (itemId: string): boolean => {
  return props.selectedOrderItems.some(item => item.id === itemId)
}

const isOrderItemAvailable = (item: CustomerOrderItem): boolean => {
  // 检查是否在不可用列表中
  if (props.unavailableItems?.[item.id]) {
    return false
  }
  
  // 检查是否有工艺流程定义
  const processFlow = (item as any).processFlow
  if (!processFlow || processFlow.length === 0) {
    return false // 未定义工艺流程的订单项不可选择
  }
  
  // 检查工艺流程是否完整
  const hasValidProcess = processFlow.every((step: any) => 
    step.stepName && step.workstation && step.estimatedDuration
  )
  
  if (!hasValidProcess) {
    return false // 工艺流程不完整的订单项不可选择
  }
  
  return true
}

const getUnavailableReason = (item: CustomerOrderItem): string => {
  // 检查具体的不可用原因
  if (props.unavailableItems?.[item.id]) {
    return props.unavailableItems[item.id]
  }
  
  const processFlow = (item as any).processFlow
  if (!processFlow || processFlow.length === 0) {
    return '未定义工艺流程'
  }
  
  const hasValidProcess = processFlow.every((step: any) => 
    step.stepName && step.workstation && step.estimatedDuration
  )
  
  if (!hasValidProcess) {
    return '工艺流程不完整'
  }
  
  return '不可用'
}

const getSelectedQuantity = (itemId: string): number => {
  const item = props.selectedOrderItems.find(item => item.id === itemId)
  return item?.selectedQuantity || 0
}

const getAvailableQuantity = (itemId: string): number => {
  const item = props.order.items.find(item => item.id === itemId)
  return item?.quantity || 0
}

const getProcessCompatibility = (itemId: string): 'compatible' | 'warning' | 'conflict' => {
  if (props.conflictingItems?.includes(itemId)) {
    return 'conflict'
  }
  
  // 简单的兼容性检查逻辑
  const selectedItems = props.selectedOrderItems
  if (selectedItems.length <= 1) return 'compatible'
  
  const currentItem = selectedItems.find(item => item.id === itemId)
  if (!currentItem) return 'compatible'
  
  const otherItems = selectedItems.filter(item => item.id !== itemId)
  const hasProcessDifference = otherItems.some(other => 
    other.processFlow.length !== currentItem.processFlow.length ||
    other.processFlow.some((step, index) => 
      step.stepName !== currentItem.processFlow[index]?.stepName
    )
  )
  
  return hasProcessDifference ? 'warning' : 'compatible'
}

const showBatchHint = (item: CustomerOrderItem): boolean => {
  // 显示批次提示的条件
  return isOrderItemSelected(item.id) && getSelectedQuantity(item.id) > 50
}

const getBatchHint = (item: CustomerOrderItem): string => {
  const quantity = getSelectedQuantity(item.id)
  if (quantity > 200) {
    return '建议拆分为多个批次以提高生产效率'
  } else if (quantity > 100) {
    return '可考虑与相似规格产品合并批次'
  }
  return '当前数量适合单批次生产'
}

const getProcessSummary = (processFlow: any[] | undefined): string => {
  if (!processFlow || processFlow.length === 0) return '未定义'
  return processFlow.slice(0, 3).map((step: any) => step.stepName).join(' → ') + 
         (processFlow.length > 3 ? '...' : '')
}

const getOrderStatusVariant = (status: string) => {
  switch (status) {
    case 'confirmed': return 'default'
    case 'ready': return 'secondary'
    case 'partial': return 'outline'
    default: return 'secondary'
  }
}

const getOrderStatusText = (status: string): string => {
  switch (status) {
    case 'confirmed': return '已确认'
    case 'ready': return '待转换'
    case 'partial': return '部分转换'
    default: return status
  }
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>