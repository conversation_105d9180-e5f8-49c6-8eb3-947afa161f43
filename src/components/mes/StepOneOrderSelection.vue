<template>
  <div class="step-one-container h-full flex flex-col">
    <!-- 搜索和筛选 -->
    <div class="search-filter-section p-4 border-b bg-white">
      <OrderItemSearchFilter
        :search-query="searchQuery"
        :status-filter="statusFilter"
        :process-type-filter="processTypeFilter"
        :customer-filter="customerFilter"
        :available-orders="availableOrders"
        :filtered-count="filteredOrders.length"
        :total-count="availableOrders.length"
        @search-changed="handleSearchChanged"
        @filter-changed="handleFilterChanged"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex min-h-0">
      <!-- 订单列表 (70%) -->
      <div class="flex-1 order-list-section">
        <div class="h-full overflow-y-auto p-4">
          <div v-if="loading" class="flex items-center justify-center h-32">
            <div class="text-center">
              <div
                class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"
              ></div>
              <span class="text-sm text-gray-500">加载订单数据...</span>
            </div>
          </div>

          <div
            v-else-if="filteredOrders.length === 0"
            class="flex flex-col items-center justify-center h-32 text-gray-500"
          >
            <Package class="w-12 h-12 mb-3 text-gray-400" />
            <span class="text-sm">暂无可用订单</span>
          </div>

          <div v-else class="space-y-4">
            <OrderItemSelector
              v-for="order in filteredOrders"
              :key="order.id"
              :order="order"
              :selected-order-items="selectedOrderItems"
              :conflicting-items="conflictingItems"
              :unavailable-items="unavailableItems"
              @order-item-selected="handleOrderItemSelected"
              @order-item-removed="handleOrderItemRemoved"
              @quantity-changed="handleQuantityChanged"
              @configure-process="handleConfigureProcess"
              class="order-card-enhanced"
            />
          </div>
        </div>
      </div>

      <!-- 已选项汇总 (30%) -->
      <div class="w-80 selected-summary-section border-l bg-gray-50">
        <SelectedItemsSummary
          :selected-items="selectedOrderItems"
          :total-quantity="totalSelectedQuantity"
          :unique-customers="uniqueCustomers"
          :has-conflicts="hasProcessConflicts"
          @item-removed="handleOrderItemRemoved"
          @quantity-changed="handleQuantityChanged"
          @clear-all="clearAllSelections"
        />
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="step-footer p-4 border-t bg-white">
      <div class="flex items-center justify-between">
        <!-- 统计信息卡片 -->
        <div class="flex items-center gap-4">
          <div
            class="stats-summary flex items-center gap-4 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200"
          >
            <div class="stat-item text-center">
              <div class="stat-label text-xs text-gray-600">总数量</div>
              <div class="stat-value text-sm font-semibold text-blue-600">
                {{ totalSelectedQuantity }}片
              </div>
            </div>
            <div class="w-px h-8 bg-blue-200"></div>
            <div class="stat-item text-center">
              <div class="stat-label text-xs text-gray-600">客户数</div>
              <div class="stat-value text-sm font-semibold text-green-600">
                {{ uniqueCustomers }}家
              </div>
            </div>
          </div>
          <div class="text-sm text-gray-600">
            已选择
            <span class="font-medium text-blue-600">{{
              selectedOrderItems.length
            }}</span>
            个订单项
          </div>
        </div>
        <div class="flex items-center gap-3">
          <Button variant="outline" @click="$emit('cancel')"> 取消 </Button>
          <Button
            @click="$emit('next-step')"
            :disabled="selectedOrderItems.length === 0"
            class="bg-blue-600 hover:bg-blue-700"
          >
            下一步：优化批次
            <ArrowRight class="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 工艺流程配置对话框 -->
    <ProcessFlowConfigDialog
      :open="showProcessConfig"
      :order-item="configOrderItem"
      @update:open="showProcessConfig = $event"
      @process-configured="handleProcessConfigured"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { Button } from "@/components/ui/button";
import { Package, ArrowRight } from "lucide-vue-next";

import OrderItemSearchFilter from "./OrderItemSearchFilter.vue";
import OrderItemSelector from "./OrderItemSelector.vue";
import SelectedItemsSummary from "./SelectedItemsSummary.vue";
import ProcessFlowConfigDialog from "./ProcessFlowConfigDialog.vue";

import type { CustomerOrder, CustomerOrderItem } from "@/types/mes-validation";
import type { SelectedOrderItem } from "@/types/production-order-creation";

// Props
interface Props {
  availableOrders: CustomerOrder[];
  selectedOrderItems: SelectedOrderItem[];
  searchQuery: string;
  statusFilter: string;
  processTypeFilter: string;
  customerFilter: string;
  loading: boolean;
  conflictingItems?: string[];
  unavailableItems?: Record<string, string>;
}

// Events
interface Emits {
  (e: "order-item-selected", item: CustomerOrderItem, quantity: number): void;
  (e: "order-item-removed", itemId: string): void;
  (e: "quantity-changed", itemId: string, quantity: number): void;
  (e: "search-changed", query: string): void;
  (e: "filter-changed", filterType: string, value: string): void;
  (e: "next-step"): void;
  (e: "cancel"): void;
}

const props = withDefaults(defineProps<Props>(), {
  conflictingItems: () => [],
  unavailableItems: () => ({}),
});

const emit = defineEmits<Emits>();

// 工艺流程配置状态
const showProcessConfig = ref(false);
const configOrderItem = ref<CustomerOrderItem | null>(null);

// 计算属性
const filteredOrders = computed(() => {
  let orders = props.availableOrders;

  // 搜索过滤
  if (props.searchQuery) {
    const query = props.searchQuery.toLowerCase();
    orders = orders.filter(
      (order) =>
        order.orderNumber.toLowerCase().includes(query) ||
        order.customerName.toLowerCase().includes(query) ||
        order.items.some(
          (item) =>
            `${item.specifications.length}×${item.specifications.width}×${item.specifications.thickness}`.includes(
              query
            ) ||
            item.specifications.glassType.toLowerCase().includes(query) ||
            item.specifications.color?.toLowerCase().includes(query)
        )
    );
  }

  // 状态过滤
  if (props.statusFilter !== "all") {
    orders = orders.filter((order) => order.status === props.statusFilter);
  }

  // 工艺类型过滤
  if (props.processTypeFilter !== "all") {
    orders = orders.filter((order) =>
      order.items.some((item) =>
        (item as any).processFlow?.some((step: any) =>
          step.stepName.toLowerCase().includes(props.processTypeFilter)
        )
      )
    );
  }

  // 客户过滤
  if (props.customerFilter !== "all") {
    orders = orders.filter(
      (order) => order.customerName === props.customerFilter
    );
  }

  return orders;
});

const totalSelectedQuantity = computed(() => {
  return props.selectedOrderItems.reduce(
    (sum, item) => sum + item.selectedQuantity,
    0
  );
});

const uniqueCustomers = computed(() => {
  const customers = new Set(
    props.selectedOrderItems.map((item) => item.customerName)
  );
  return customers.size;
});

const hasProcessConflicts = computed(() => {
  return props.conflictingItems && props.conflictingItems.length > 0;
});

// 事件处理
const handleOrderItemSelected = (item: CustomerOrderItem, quantity: number) => {
  emit("order-item-selected", item, quantity);
};

const handleOrderItemRemoved = (itemId: string) => {
  emit("order-item-removed", itemId);
};

const handleQuantityChanged = (itemId: string, quantity: number) => {
  emit("quantity-changed", itemId, quantity);
};

const handleSearchChanged = (query: string) => {
  emit("search-changed", query);
};

const handleFilterChanged = (filterType: string, value: string) => {
  emit("filter-changed", filterType, value);
};

const clearAllSelections = () => {
  props.selectedOrderItems.forEach((item) => {
    emit("order-item-removed", item.id);
  });
};

const handleConfigureProcess = (orderItem: CustomerOrderItem) => {
  configOrderItem.value = orderItem;
  showProcessConfig.value = true;
};

const handleProcessConfigured = (
  orderItem: CustomerOrderItem,
  processFlow: any[]
) => {
  // 更新订单项的工艺流程
  (orderItem as any).processFlow = processFlow;

  // 通知父组件工艺流程已配置
  console.log("工艺流程已配置:", orderItem, processFlow);

  // 可以触发重新验证订单项可用性
  // emit('process-flow-updated', orderItem, processFlow)
};
</script>

<style scoped>
.step-one-container {
  background: #fafafa;
}

.search-filter-section {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.order-list-section {
  background: white;
}

.selected-summary-section {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  border-left: 1px solid #e2e8f0;
}

.step-footer {
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

.stats-summary {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
}

.stat-item {
  min-width: 60px;
}

.stat-label {
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  margin-top: 2px;
}

.order-card-enhanced {
  transition: all 0.2s ease;
  border-radius: 8px;
  overflow: hidden;
}

.order-card-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .selected-summary-section {
    width: 320px;
  }
}

@media (max-width: 768px) {
  .flex-1.flex {
    flex-direction: column;
  }

  .selected-summary-section {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e2e8f0;
    max-height: 300px;
  }
}
</style>
