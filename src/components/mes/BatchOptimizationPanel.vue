<template>
  <div class="h-full flex flex-col">
    <!-- 头部标题 -->
    <div class="p-4 border-b bg-white">
      <div class="flex items-center justify-between">
        <h3 class="font-medium flex items-center gap-2">
          <Zap class="w-4 h-4" />
          批次优化方案
        </h3>
        <div class="flex items-center gap-2">
          <Button 
            size="sm" 
            variant="outline"
            @click="handleOptimizationRequested"
            :disabled="selectedOrderItems.length === 0 || loading"
            title="重新分析批次分组，优化生产效率和资源利用率"
          >
            <RefreshCw class="w-3 h-3 mr-1" :class="{ 'animate-spin': loading }" />
            重新优化
          </Button>
          
          <!-- 优化说明 -->
          <div class="text-xs text-gray-500 mt-1">
            智能分析工艺兼容性，优化批次分组
          </div>
        </div>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="dialog-panel-content">
      <!-- 空状态 -->
      <div v-if="selectedOrderItems.length === 0" class="p-8 text-center text-gray-500">
        <Package class="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <div class="text-sm font-medium mb-2">请先选择订单项</div>
        <div class="text-xs">选择订单项后，系统将自动生成优化的批次方案</div>
      </div>
      
      <!-- 加载状态 -->
      <div v-else-if="loading" class="p-8 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <div class="text-sm text-gray-600">正在优化批次方案...</div>
      </div>
      
      <!-- 优化结果 -->
      <div v-else-if="batchOptimization" class="p-4 space-y-6">
        <!-- 优化效果展示 -->
        <Card>
          <CardHeader class="pb-3">
            <CardTitle class="text-sm flex items-center gap-2">
              <TrendingUp class="w-4 h-4 text-green-600" />
              优化效果
              <Badge variant="outline" class="text-xs">智能算法</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-3 gap-4">
              <div class="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                <div class="text-lg font-bold text-green-700">+{{ batchOptimization.efficiency }}%</div>
                <div class="text-xs text-green-600">效率提升</div>
                <div class="text-xs text-gray-500 mt-1">vs 单独生产</div>
              </div>
              <div class="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div class="text-lg font-bold text-blue-700">{{ batchOptimization.timeSaved }}h</div>
                <div class="text-xs text-blue-600">节省工时</div>
                <div class="text-xs text-gray-500 mt-1">减少换线</div>
              </div>
              <div class="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
                <div class="text-lg font-bold text-purple-700">{{ batchOptimization.batches.length }}</div>
                <div class="text-xs text-purple-600">推荐批次</div>
                <div class="text-xs text-gray-500 mt-1">最优分组</div>
              </div>
            </div>
            
            <!-- 优化统计 -->
            <div class="mt-4 grid grid-cols-2 gap-4 text-xs">
              <div class="flex justify-between p-2 bg-gray-50 rounded">
                <span class="text-gray-600">总订单项:</span>
                <span class="font-medium">{{ batchOptimization.totalItems }}项</span>
              </div>
              <div class="flex justify-between p-2 bg-gray-50 rounded">
                <span class="text-gray-600">总数量:</span>
                <span class="font-medium">{{ batchOptimization.totalQuantity }}片</span>
              </div>
            </div>
            
            <!-- 推荐说明 -->
            <div v-if="batchOptimization.recommendations.length > 0" class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div class="text-sm font-medium text-blue-800 mb-2 flex items-center gap-2">
                <Lightbulb class="w-4 h-4" />
                优化建议
              </div>
              <ul class="text-xs text-blue-700 space-y-1">
                <li v-for="(rec, index) in batchOptimization.recommendations" :key="index" class="flex items-start gap-2">
                  <div class="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  {{ rec }}
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
        
        <!-- 批次详情 -->
        <div class="space-y-4">
          <div 
            v-for="(batch, index) in batchOptimization.batches" 
            :key="batch.id"
            class="border rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow"
          >
            <!-- 批次头部 -->
            <div class="p-4 bg-gray-50 border-b rounded-t-lg">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="flex items-center gap-2">
                    <div class="w-6 h-6 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-xs font-bold">
                      {{ index + 1 }}
                    </div>
                    <h4 class="font-medium">{{ batch.name || `批次 ${index + 1}` }}</h4>
                  </div>
                  <Badge variant="outline" class="text-xs">{{ batch.totalQuantity }}片</Badge>
                  <Badge :variant="getPriorityVariant(batch.priority)" class="text-xs">
                    {{ getPriorityText(batch.priority) }}
                  </Badge>
                  <Badge 
                    :variant="getUtilizationVariant(batch.utilization)" 
                    class="text-xs"
                  >
                    {{ batch.utilization }}% 利用率
                  </Badge>
                </div>
                <div class="flex items-center gap-2">
                  <Badge variant="secondary" class="text-xs">{{ batch.workstationGroup }}</Badge>
                  <Button size="sm" variant="ghost" @click="handleBatchDetailsRequested(batch.id)">
                    <Eye class="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <!-- 批次摘要 -->
              <div class="mt-2 flex items-center gap-4 text-xs text-gray-600">
                <span class="flex items-center gap-1">
                  <Package class="w-3 h-3" />
                  {{ batch.items.length }}个订单项
                </span>
                <span class="flex items-center gap-1">
                  <Clock class="w-3 h-3" />
                  {{ Math.round(batch.estimatedTime / 60 * 10) / 10 }}小时
                </span>
                <span class="flex items-center gap-1">
                  <Users class="w-3 h-3" />
                  {{ getUniqueCustomers(batch.items) }}个客户
                </span>
              </div>
            </div>
            
            <!-- 批次内容 -->
            <div class="p-4">
              <!-- 订单项列表 -->
              <div class="space-y-2 mb-4">
                <div class="flex items-center justify-between">
                  <div class="text-sm font-medium text-gray-700">包含订单项</div>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    @click="toggleBatchExpansion(batch.id)"
                    class="text-xs"
                  >
                    {{ expandedBatches.has(batch.id) ? '收起' : '展开' }}
                    <ChevronDown 
                      class="w-3 h-3 ml-1 transition-transform" 
                      :class="{ 'rotate-180': expandedBatches.has(batch.id) }"
                    />
                  </Button>
                </div>
                
                <div 
                  v-if="expandedBatches.has(batch.id)"
                  class="space-y-1"
                >
                  <div 
                    v-for="item in batch.items" 
                    :key="item.id"
                    class="flex items-center justify-between text-sm p-2 bg-gray-50 rounded border hover:bg-gray-100 transition-colors"
                  >
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">{{ item.orderNumber }}</span>
                        <Badge variant="outline" class="text-xs">{{ item.customerName }}</Badge>
                      </div>
                      <div class="text-xs text-gray-600 mt-1">
                        {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                        {{ item.specifications.glassType }} {{ item.specifications.color }}
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="font-medium text-sm">{{ item.selectedQuantity }}片</div>
                      <div class="text-xs text-gray-500">
                        {{ formatDate(item.deliveryDate) }}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-else class="text-xs text-gray-500">
                  {{ batch.items.slice(0, 2).map(item => item.orderNumber).join(', ') }}
                  <span v-if="batch.items.length > 2"> 等{{ batch.items.length }}项</span>
                </div>
              </div>
              
              <!-- 工艺流程 -->
              <div class="mb-4">
                <div class="text-sm font-medium text-gray-700 mb-2">工艺流程</div>
                <div class="flex items-center gap-2 text-sm flex-wrap">
                  <div 
                    v-for="(step, stepIndex) in batch.processFlow" 
                    :key="stepIndex"
                    class="flex items-center gap-2"
                  >
                    <div class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs flex items-center gap-1">
                      <component :is="getStepIcon(step.stepName)" class="w-3 h-3" />
                      {{ step.stepName }}
                    </div>
                    <ArrowRight v-if="stepIndex < batch.processFlow.length - 1" class="w-3 h-3 text-gray-400" />
                  </div>
                </div>
              </div>
              
              <!-- 批次指标 -->
              <div class="grid grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg">
                <div class="text-center">
                  <div class="text-sm font-bold text-gray-700">{{ Math.round(batch.estimatedTime / 60 * 10) / 10 }}h</div>
                  <div class="text-xs text-gray-600">预估工时</div>
                </div>
                <div class="text-center">
                  <div class="text-sm font-bold" :class="getUtilizationColor(batch.utilization)">
                    {{ batch.utilization }}%
                  </div>
                  <div class="text-xs text-gray-600">设备利用率</div>
                </div>
                <div class="text-center">
                  <div class="text-sm font-bold text-gray-700">
                    {{ Math.round(batch.totalQuantity / (batch.estimatedTime / 60) * 10) / 10 }}
                  </div>
                  <div class="text-xs text-gray-600">片/小时</div>
                </div>
              </div>
              
              <!-- 冲突级别指示 -->
              <div v-if="batch.conflictLevel !== 'none'" class="mt-3 p-2 rounded" 
                   :class="getConflictLevelClass(batch.conflictLevel)">
                <div class="flex items-center gap-2 text-xs">
                  <AlertTriangle class="w-3 h-3" />
                  <span class="font-medium">{{ getConflictLevelText(batch.conflictLevel) }}</span>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    @click="resolveBatchConflict(batch.id)"
                    class="ml-auto text-xs"
                  >
                    解决
                  </Button>
                </div>
              </div>
              
              <!-- 批次操作 -->
              <div class="mt-4 pt-3 border-t flex gap-2">
                <Button 
                  size="sm" 
                  variant="outline"
                  @click="editBatch(batch)"
                  class="text-xs"
                >
                  <Edit class="w-3 h-3 mr-1" />
                  编辑
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  @click="duplicateBatch(batch)"
                  class="text-xs"
                >
                  <Copy class="w-3 h-3 mr-1" />
                  复制
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost"
                  @click="removeBatch(batch.id)"
                  class="text-xs text-red-600 hover:text-red-700"
                >
                  <Trash class="w-3 h-3 mr-1" />
                  移除
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 工艺冲突提示 -->
      <div v-if="processConflicts.length > 0" class="p-4">
        <Card>
          <CardHeader class="pb-3">
            <CardTitle class="text-sm flex items-center gap-2 text-yellow-700">
              <AlertTriangle class="w-4 h-4" />
              工艺冲突检测
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div 
                v-for="conflict in processConflicts" 
                :key="conflict.id"
                class="p-3 border rounded-lg"
                :class="getConflictSeverityClass(conflict.severity)"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="font-medium text-sm">{{ conflict.description }}</div>
                    <div class="text-xs text-gray-600 mt-1">
                      影响订单项: {{ conflict.affectedItems.length }}个
                    </div>
                    <div class="text-xs text-gray-600 mt-1">
                      冲突类型: {{ getConflictTypeText(conflict.conflictType) }}
                    </div>
                  </div>
                  <Badge :variant="conflict.severity === 'error' ? 'destructive' : 'secondary'" class="text-xs">
                    {{ conflict.severity === 'error' ? '严重' : '警告' }}
                  </Badge>
                </div>
                
                <!-- 解决建议 -->
                <div v-if="conflict.suggestions.length > 0" class="mt-3 pt-3 border-t">
                  <div class="text-xs font-medium text-gray-700 mb-2">解决建议:</div>
                  <ul class="text-xs text-gray-600 space-y-1">
                    <li v-for="(suggestion, index) in conflict.suggestions" :key="index" class="flex items-start gap-2">
                      <div class="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                      {{ suggestion }}
                    </li>
                  </ul>
                </div>
                
                <!-- 操作按钮 -->
                <div class="mt-3 flex gap-2">
                  <Button 
                    v-if="conflict.autoResolvable"
                    size="sm" 
                    variant="outline"
                    @click="handleConflictResolved(conflict.id, 'auto')"
                  >
                    自动解决
                  </Button>
                  <Button 
                    size="sm" 
                    variant="ghost"
                    @click="handleConflictResolved(conflict.id, 'ignore')"
                  >
                    忽略
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <!-- 验证结果 -->
      <div v-if="validationResults.length > 0" class="p-4">
        <Card>
          <CardHeader class="pb-3">
            <CardTitle class="text-sm flex items-center gap-2">
              <CheckCircle class="w-4 h-4" />
              验证结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <div 
                v-for="result in validationResults" 
                :key="result.id"
                class="flex items-start gap-2 p-2 rounded text-sm"
                :class="getValidationClass(result.level)"
              >
                <component 
                  :is="getValidationIcon(result.level)" 
                  class="w-4 h-4 mt-0.5 flex-shrink-0" 
                />
                <div class="flex-1">
                  <div class="font-medium">{{ result.title }}</div>
                  <div class="text-xs mt-1">{{ result.message }}</div>
                  <div v-if="result.suggestion" class="text-xs mt-1 text-blue-600">
                    建议: {{ result.suggestion }}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Zap,
  RefreshCw,
  Package,
  TrendingUp,
  Lightbulb,
  Eye,
  ArrowRight,
  Clock,
  Activity,
  AlertTriangle,
  CheckCircle,
  Info,
  Users,
  ChevronDown,
  Edit,
  Copy,
  Trash,
  Scissors,
  Flame,
  Layers,
} from 'lucide-vue-next'

import type { 
  BatchOptimizationPanelProps,
  BatchOptimizationPanelEvents
} from '@/types/production-order-creation'

// Props
defineProps<BatchOptimizationPanelProps>()

// Events
const emit = defineEmits<BatchOptimizationPanelEvents>()

// 响应式数据
const expandedBatches = ref(new Set<string>())

// 事件处理
const handleOptimizationRequested = () => {
  emit('optimization-requested')
}

const handleBatchDetailsRequested = (batchId: string) => {
  emit('batch-details-requested', batchId)
}

const handleConflictResolved = (conflictId: string, resolution: string) => {
  emit('conflict-resolved', conflictId, resolution)
}

// 工具方法
const getPriorityVariant = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'destructive'
    case 'high': return 'default'
    case 'normal': return 'secondary'
    case 'low': return 'outline'
    default: return 'secondary'
  }
}

const getPriorityText = (priority: string): string => {
  switch (priority) {
    case 'urgent': return '紧急'
    case 'high': return '高'
    case 'normal': return '普通'
    case 'low': return '低'
    default: return priority
  }
}

const getConflictLevelClass = (level: string): string => {
  switch (level) {
    case 'minor': return 'bg-yellow-50 border border-yellow-200 text-yellow-700'
    case 'major': return 'bg-red-50 border border-red-200 text-red-700'
    default: return ''
  }
}

const getConflictLevelText = (level: string): string => {
  switch (level) {
    case 'minor': return '轻微冲突'
    case 'major': return '严重冲突'
    default: return '无冲突'
  }
}

const getConflictSeverityClass = (severity: string): string => {
  switch (severity) {
    case 'error': return 'bg-red-50 border-red-200'
    case 'warning': return 'bg-yellow-50 border-yellow-200'
    default: return 'bg-gray-50 border-gray-200'
  }
}

const getConflictTypeText = (type: string): string => {
  switch (type) {
    case 'parameter': return '参数冲突'
    case 'sequence': return '工序冲突'
    case 'equipment': return '设备冲突'
    case 'material': return '材料冲突'
    default: return type
  }
}

const getValidationClass = (level: string): string => {
  switch (level) {
    case 'error': return 'bg-red-50 text-red-700'
    case 'warning': return 'bg-yellow-50 text-yellow-700'
    case 'info': return 'bg-blue-50 text-blue-700'
    default: return 'bg-gray-50 text-gray-700'
  }
}

const getValidationIcon = (level: string) => {
  switch (level) {
    case 'error': return AlertTriangle
    case 'warning': return AlertTriangle
    case 'info': return Info
    default: return CheckCircle
  }
}

const getUtilizationVariant = (utilization: number) => {
  if (utilization >= 85) return 'default'
  if (utilization >= 70) return 'secondary'
  return 'outline'
}

const getUtilizationColor = (utilization: number): string => {
  if (utilization >= 85) return 'text-green-700'
  if (utilization >= 70) return 'text-blue-700'
  return 'text-yellow-700'
}

const getUniqueCustomers = (items: any[]): number => {
  return new Set(items.map(item => item.customerName)).size
}

const toggleBatchExpansion = (batchId: string) => {
  if (expandedBatches.value.has(batchId)) {
    expandedBatches.value.delete(batchId)
  } else {
    expandedBatches.value.add(batchId)
  }
}

const getStepIcon = (stepName: string) => {
  switch (stepName.toLowerCase()) {
    case '切割': return Scissors
    case '钢化': return Flame
    case '夹胶':
    case '合片': return Layers
    default: return Activity
  }
}

const editBatch = (batch: unknown) => {
  console.log('编辑批次:', batch.id)
  emit('batch-modified', batch.id, { batchId: batch.id })
}

const duplicateBatch = (batch: any) => {
  console.log('复制批次:', (batch as unknown).id)
}

const removeBatch = (batchId: string) => {
  console.log('移除批次:', batchId)
}

const resolveBatchConflict = (batchId: string) => {
  console.log('解决批次冲突:', batchId)
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
}
</script>