import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  Component,
  Assembly,
  ProductStructure,
  Product,
  QuoteBOM,
  ProductionBOM,
  ComponentFilters,
  AssemblyFilters,
  ProductStructureFilters,
  ProductFilters,
  QuoteBOMFilters,
  ProductionBOMFilters
} from '@/types/product';
import {
  componentService,
  assemblyService,
  productStructureService,
  productService,
  quoteBOMService,
  productionBOMService
} from '@/services/productService';

// 组件管理Store
export const useComponentStore = defineStore('component', () => {
  // 状态
  const components = ref<Component[]>([]);
  const currentComponent = ref<Component | null>(null);
  const filters = ref<ComponentFilters>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性
  const filteredComponents = computed(() => {
    if (!filters.value) return components.value;
    
    return components.value.filter(component => {
      if (filters.value.componentType && component.componentType !== filters.value.componentType) {
        return false;
      }
      if (filters.value.materialCategoryId && component.materialCategoryId !== filters.value.materialCategoryId) {
        return false;
      }
      if (filters.value.status && component.status !== filters.value.status) {
        return false;
      }
      if (filters.value.search) {
        const searchLower = filters.value.search.toLowerCase();
        return (
          component.name.toLowerCase().includes(searchLower) ||
          component.code.toLowerCase().includes(searchLower) ||
          component.description?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  });

  const componentsByType = computed(() => {
    const grouped: Record<string, Component[]> = {};
    components.value.forEach(component => {
      if (!grouped[component.componentType]) {
        grouped[component.componentType] = [];
      }
      grouped[component.componentType].push(component);
    });
    return grouped;
  });

  // 操作方法
  const loadComponents = async (newFilters?: ComponentFilters) => {
    loading.value = true;
    error.value = null;
    
    try {
      if (newFilters) {
        filters.value = newFilters;
      }
      components.value = await componentService.getComponents(filters.value);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载组件失败';
      console.error('Error loading components:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadComponentById = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      currentComponent.value = await componentService.getComponentById(id);
      if (!currentComponent.value) {
        error.value = `未找到ID为 ${id} 的组件`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载组件详情失败';
      console.error('Error loading component:', err);
    } finally {
      loading.value = false;
    }
  };

  const createComponent = async (componentData: Partial<Component>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const newComponent = await componentService.createComponent(componentData);
      components.value.push(newComponent);
      return newComponent;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建组件失败';
      console.error('Error creating component:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateComponent = async (id: string, updates: Partial<Component>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const updatedComponent = await componentService.updateComponent(id, updates);
      const index = components.value.findIndex(c => c.id === id);
      if (index !== -1) {
        components.value[index] = updatedComponent;
      }
      if (currentComponent.value?.id === id) {
        currentComponent.value = updatedComponent;
      }
      return updatedComponent;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新组件失败';
      console.error('Error updating component:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteComponent = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      await componentService.deleteComponent(id);
      components.value = components.value.filter(c => c.id !== id);
      if (currentComponent.value?.id === id) {
        currentComponent.value = null;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除组件失败';
      console.error('Error deleting component:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const setFilters = (newFilters: ComponentFilters) => {
    filters.value = newFilters;
  };

  const clearError = () => {
    error.value = null;
  };

  const reset = () => {
    components.value = [];
    currentComponent.value = null;
    filters.value = {};
    loading.value = false;
    error.value = null;
  };

  return {
    // 状态
    components,
    currentComponent,
    filters,
    loading,
    error,
    
    // 计算属性
    filteredComponents,
    componentsByType,
    
    // 方法
    loadComponents,
    loadComponentById,
    createComponent,
    updateComponent,
    deleteComponent,
    setFilters,
    clearError,
    reset
  };
});

// 构件管理Store
export const useAssemblyStore = defineStore('assembly', () => {
  const assemblies = ref<Assembly[]>([]);
  const currentAssembly = ref<Assembly | null>(null);
  const filters = ref<AssemblyFilters>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  const filteredAssemblies = computed(() => {
    if (!filters.value) return assemblies.value;
    
    return assemblies.value.filter(assembly => {
      if (filters.value.assemblyType && assembly.assemblyType !== filters.value.assemblyType) {
        return false;
      }
      if (filters.value.status && assembly.status !== filters.value.status) {
        return false;
      }
      if (filters.value.search) {
        const searchLower = filters.value.search.toLowerCase();
        return (
          assembly.name.toLowerCase().includes(searchLower) ||
          assembly.code.toLowerCase().includes(searchLower) ||
          assembly.description?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  });

  const loadAssemblies = async (newFilters?: AssemblyFilters) => {
    loading.value = true;
    error.value = null;
    
    try {
      if (newFilters) {
        filters.value = newFilters;
      }
      assemblies.value = await assemblyService.getAssemblies(filters.value);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载构件失败';
      console.error('Error loading assemblies:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadAssemblyById = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      currentAssembly.value = await assemblyService.getAssemblyById(id);
      if (!currentAssembly.value) {
        error.value = `未找到ID为 ${id} 的构件`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载构件详情失败';
      console.error('Error loading assembly:', err);
    } finally {
      loading.value = false;
    }
  };

  const createAssembly = async (assemblyData: Partial<Assembly>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const newAssembly = await assemblyService.createAssembly(assemblyData);
      assemblies.value.push(newAssembly);
      return newAssembly;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建构件失败';
      console.error('Error creating assembly:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateAssembly = async (id: string, updates: Partial<Assembly>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const updatedAssembly = await assemblyService.updateAssembly(id, updates);
      const index = assemblies.value.findIndex(a => a.id === id);
      if (index !== -1) {
        assemblies.value[index] = updatedAssembly;
      }
      if (currentAssembly.value?.id === id) {
        currentAssembly.value = updatedAssembly;
      }
      return updatedAssembly;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新构件失败';
      console.error('Error updating assembly:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteAssembly = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      await assemblyService.deleteAssembly(id);
      assemblies.value = assemblies.value.filter(a => a.id !== id);
      if (currentAssembly.value?.id === id) {
        currentAssembly.value = null;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除构件失败';
      console.error('Error deleting assembly:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    assemblies,
    currentAssembly,
    filters,
    loading,
    error,
    filteredAssemblies,
    loadAssemblies,
    loadAssemblyById,
    createAssembly,
    updateAssembly,
    deleteAssembly
  };
});

// 产品结构管理Store
export const useProductStructureStore = defineStore('productStructure', () => {
  const structures = ref<ProductStructure[]>([]);
  const currentStructure = ref<ProductStructure | null>(null);
  const filters = ref<ProductStructureFilters>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  const filteredStructures = computed(() => {
    if (!filters.value) return structures.value;

    return structures.value.filter(structure => {
      if (filters.value.productType && structure.productType !== filters.value.productType) {
        return false;
      }
      if (filters.value.category && structure.category !== filters.value.category) {
        return false;
      }
      if (filters.value.status && structure.status !== filters.value.status) {
        return false;
      }
      if (filters.value.search) {
        const searchLower = filters.value.search.toLowerCase();
        return (
          structure.name.toLowerCase().includes(searchLower) ||
          structure.code.toLowerCase().includes(searchLower) ||
          structure.description?.toLowerCase().includes(searchLower)
        );
      }
      if (filters.value.tags && filters.value.tags.length > 0) {
        return filters.value.tags.some(tag => structure.tags.includes(tag));
      }
      return true;
    });
  });

  const loadStructures = async (newFilters?: ProductStructureFilters) => {
    loading.value = true;
    error.value = null;

    try {
      if (newFilters) {
        filters.value = newFilters;
      }
      structures.value = await productStructureService.getStructures(filters.value);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载产品结构失败';
      console.error('Error loading product structures:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadStructureById = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      currentStructure.value = await productStructureService.getStructureById(id);
      if (!currentStructure.value) {
        error.value = `未找到ID为 ${id} 的产品结构`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载产品结构详情失败';
      console.error('Error loading product structure:', err);
    } finally {
      loading.value = false;
    }
  };

  return {
    structures,
    currentStructure,
    filters,
    loading,
    error,
    filteredStructures,
    loadStructures,
    loadStructureById
  };
});

// 产品管理Store
export const useProductStore = defineStore('product', () => {
  const products = ref<Product[]>([]);
  const currentProduct = ref<Product | null>(null);
  const filters = ref<ProductFilters>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  const filteredProducts = computed(() => {
    if (!filters.value) return products.value;

    return products.value.filter(product => {
      if (filters.value.productStructureId && product.productStructureId !== filters.value.productStructureId) {
        return false;
      }
      if (filters.value.category && product.category !== filters.value.category) {
        return false;
      }
      if (filters.value.lifecycle && product.lifecycle !== filters.value.lifecycle) {
        return false;
      }
      if (filters.value.status && product.status !== filters.value.status) {
        return false;
      }
      if (filters.value.search) {
        const searchLower = filters.value.search.toLowerCase();
        return (
          product.name.toLowerCase().includes(searchLower) ||
          product.code.toLowerCase().includes(searchLower) ||
          product.description?.toLowerCase().includes(searchLower)
        );
      }
      if (filters.value.tags && filters.value.tags.length > 0) {
        return filters.value.tags.some(tag => product.tags.includes(tag));
      }
      return true;
    });
  });

  const loadProducts = async (newFilters?: ProductFilters) => {
    loading.value = true;
    error.value = null;

    try {
      if (newFilters) {
        filters.value = newFilters;
      }
      products.value = await productService.getProducts(filters.value);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载产品失败';
      console.error('Error loading products:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadProductById = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      currentProduct.value = await productService.getProductById(id);
      if (!currentProduct.value) {
        error.value = `未找到ID为 ${id} 的产品`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载产品详情失败';
      console.error('Error loading product:', err);
    } finally {
      loading.value = false;
    }
  };

  const createProduct = async (productData: Partial<Product>) => {
    loading.value = true;
    error.value = null;

    try {
      const newProduct = await productService.createProduct(productData);
      products.value.push(newProduct);
      return newProduct;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建产品失败';
      console.error('Error creating product:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    products,
    currentProduct,
    filters,
    loading,
    error,
    filteredProducts,
    loadProducts,
    loadProductById,
    createProduct
  };
});

// BOM管理Store
export const useBOMStore = defineStore('bom', () => {
  const quoteBOMs = ref<QuoteBOM[]>([]);
  const productionBOMs = ref<ProductionBOM[]>([]);
  const currentQuoteBOM = ref<QuoteBOM | null>(null);
  const currentProductionBOM = ref<ProductionBOM | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const loadQuoteBOMs = async (filters?: QuoteBOMFilters) => {
    loading.value = true;
    error.value = null;

    try {
      quoteBOMs.value = await quoteBOMService.getQuoteBOMs(filters);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载报价BOM失败';
      console.error('Error loading quote BOMs:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadProductionBOMs = async (filters?: ProductionBOMFilters) => {
    loading.value = true;
    error.value = null;

    try {
      productionBOMs.value = await productionBOMService.getProductionBOMs(filters);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载生产BOM失败';
      console.error('Error loading production BOMs:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadQuoteBOMById = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      currentQuoteBOM.value = await quoteBOMService.getQuoteBOMById(id);
      if (!currentQuoteBOM.value) {
        error.value = `未找到ID为 ${id} 的报价BOM`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载报价BOM详情失败';
      console.error('Error loading quote BOM:', err);
    } finally {
      loading.value = false;
    }
  };

  const loadProductionBOMById = async (id: string) => {
    loading.value = true;
    error.value = null;

    try {
      currentProductionBOM.value = await productionBOMService.getProductionBOMById(id);
      if (!currentProductionBOM.value) {
        error.value = `未找到ID为 ${id} 的生产BOM`;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载生产BOM详情失败';
      console.error('Error loading production BOM:', err);
    } finally {
      loading.value = false;
    }
  };

  const convertToProductionBOM = async (quoteBOMId: string) => {
    loading.value = true;
    error.value = null;

    try {
      const productionBOM = await productionBOMService.convertFromQuoteBOM(quoteBOMId);
      productionBOMs.value.push(productionBOM);
      return productionBOM;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '转换生产BOM失败';
      console.error('Error converting to production BOM:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    quoteBOMs,
    productionBOMs,
    currentQuoteBOM,
    currentProductionBOM,
    loading,
    error,
    loadQuoteBOMs,
    loadProductionBOMs,
    loadQuoteBOMById,
    loadProductionBOMById,
    convertToProductionBOM
  };
});
