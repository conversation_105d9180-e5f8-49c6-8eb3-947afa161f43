import type {
  Component,
  Assembly,
  ProductStructure,
  Product,
  QuoteBOM,
  ProductionBOM,
  ProductConfiguration,
  ComponentFilters,
  AssemblyFilters,
  ProductStructureFilters,
  ProductFilters,
  QuoteBOMFilters,
  ProductionBOMFilters,
  ValidationResult
} from '@/types/product';

// Mock数据加载器
class MockDataLoader {
  private static cache = new Map<string, any>();

  static async loadMockData<T>(path: string): Promise<T> {
    if (this.cache.has(path)) {
      return this.cache.get(path);
    }

    try {
      const response = await fetch(`/mock/product/${path}`);
      if (!response.ok) {
        throw new Error(`Failed to load mock data: ${path}`);
      }
      const data = await response.json();
      this.cache.set(path, data);
      return data;
    } catch (error) {
      console.error(`Error loading mock data from ${path}:`, error);
      throw error;
    }
  }

  static clearCache() {
    this.cache.clear();
  }
}

// 组件服务
export class ComponentService {
  async getComponents(filters?: ComponentFilters): Promise<Component[]> {
    const data = await MockDataLoader.loadMockData<{ components: Component[] }>('components.json');
    let components = data.components;

    if (filters) {
      components = components.filter(component => {
        if (filters.componentType && component.componentType !== filters.componentType) {
          return false;
        }
        if (filters.materialCategoryId && component.materialCategoryId !== filters.materialCategoryId) {
          return false;
        }
        if (filters.status && component.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            component.name.toLowerCase().includes(searchLower) ||
            component.code.toLowerCase().includes(searchLower) ||
            component.description?.toLowerCase().includes(searchLower)
          );
        }
        return true;
      });
    }

    return components;
  }

  async getComponentById(id: string): Promise<Component | null> {
    const components = await this.getComponents();
    return components.find(c => c.id === id) || null;
  }

  async createComponent(component: Partial<Component>): Promise<Component> {
    const newComponent: Component = {
      id: `comp_${Date.now()}`,
      code: component.code || '',
      name: component.name || '',
      description: component.description,
      componentType: component.componentType || 'other',
      materialCategoryId: component.materialCategoryId || '',
      materialCategoryName: component.materialCategoryName || '',
      materialCategoryCode: component.materialCategoryCode || '',
      parameters: component.parameters || [],
      quantityFormula: component.quantityFormula,
      costFormula: component.costFormula,
      constraints: component.constraints || [],
      processRequirements: component.processRequirements || [],
      properties: component.properties || {},
      status: component.status || 'draft',
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    // 在实际应用中，这里会调用API保存到后端
    console.log('Creating component:', newComponent);
    return newComponent;
  }

  async updateComponent(id: string, updates: Partial<Component>): Promise<Component> {
    const component = await this.getComponentById(id);
    if (!component) {
      throw new Error(`Component with id ${id} not found`);
    }

    const updatedComponent: Component = {
      ...component,
      ...updates,
      id: component.id, // 确保ID不被覆盖
      version: component.version + 1,
      updatedAt: new Date().toISOString(),
      updatedBy: 'current_user'
    };

    console.log('Updating component:', updatedComponent);
    return updatedComponent;
  }

  async deleteComponent(id: string): Promise<void> {
    console.log('Deleting component:', id);
    // 在实际应用中，这里会调用API删除
  }

  async validateParameters(componentId: string, values: Record<string, any>): Promise<ValidationResult[]> {
    const component = await this.getComponentById(componentId);
    if (!component) {
      throw new Error(`Component with id ${componentId} not found`);
    }

    const results: ValidationResult[] = [];

    // 验证约束条件
    for (const constraint of component.constraints) {
      try {
        // 简单的表达式验证（实际应用中需要更复杂的表达式解析器）
        const isValid = this.evaluateConstraint(constraint.expression, values);
        if (!isValid) {
          results.push({
            ruleId: constraint.id,
            ruleName: constraint.name,
            type: constraint.severity,
            message: constraint.errorMessage,
            affectedParameters: Object.keys(values)
          });
        }
      } catch (error) {
        console.error(`Error evaluating constraint ${constraint.id}:`, error);
      }
    }

    return results;
  }

  private evaluateConstraint(expression: string, values: Record<string, any>): boolean {
    // 简化的约束评估逻辑
    // 实际应用中需要使用专业的表达式解析器
    try {
      // 替换变量名为实际值
      let evaluableExpression = expression;
      for (const [key, value] of Object.entries(values)) {
        evaluableExpression = evaluableExpression.replace(
          new RegExp(`\\b${key}\\b`, 'g'),
          String(value)
        );
      }
      
      // 简单的安全检查
      if (!/^[0-9+\-*/.()>\s<>=&|!]+$/.test(evaluableExpression)) {
        return true; // 如果表达式包含不安全字符，默认通过
      }

      return eval(evaluableExpression);
    } catch {
      return true; // 如果评估失败，默认通过
    }
  }

  async calculateQuantity(componentId: string, parameters: Record<string, any>): Promise<number> {
    const component = await this.getComponentById(componentId);
    if (!component || !component.quantityFormula) {
      return 1;
    }

    try {
      // 简化的公式计算逻辑
      let formula = component.quantityFormula;
      for (const [key, value] of Object.entries(parameters)) {
        formula = formula.replace(new RegExp(`\\b${key}\\b`, 'g'), String(value));
      }
      
      return eval(formula) || 1;
    } catch (error) {
      console.error(`Error calculating quantity for component ${componentId}:`, error);
      return 1;
    }
  }
}

// 构件服务
export class AssemblyService {
  async getAssemblies(filters?: AssemblyFilters): Promise<Assembly[]> {
    const data = await MockDataLoader.loadMockData<{ assemblies: Assembly[] }>('assemblies.json');
    let assemblies = data.assemblies;

    if (filters) {
      assemblies = assemblies.filter(assembly => {
        if (filters.assemblyType && assembly.assemblyType !== filters.assemblyType) {
          return false;
        }
        if (filters.status && assembly.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            assembly.name.toLowerCase().includes(searchLower) ||
            assembly.code.toLowerCase().includes(searchLower) ||
            assembly.description?.toLowerCase().includes(searchLower)
          );
        }
        return true;
      });
    }

    return assemblies;
  }

  async getAssemblyById(id: string): Promise<Assembly | null> {
    const assemblies = await this.getAssemblies();
    return assemblies.find(a => a.id === id) || null;
  }

  async createAssembly(assembly: Partial<Assembly>): Promise<Assembly> {
    const newAssembly: Assembly = {
      id: `asm_${Date.now()}`,
      code: assembly.code || '',
      name: assembly.name || '',
      description: assembly.description,
      assemblyType: assembly.assemblyType || 'complete_assembly',
      componentInstances: assembly.componentInstances || [],
      subAssemblies: assembly.subAssemblies || [],
      assemblyParameters: assembly.assemblyParameters || [],
      assemblyConstraints: assembly.assemblyConstraints || [],
      assemblyProcess: assembly.assemblyProcess || {
        id: `proc_${Date.now()}`,
        processName: 'Default Process',
        steps: [],
        totalEstimatedTime: 0
      },
      qualityRequirements: assembly.qualityRequirements || [],
      status: assembly.status || 'draft',
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Creating assembly:', newAssembly);
    return newAssembly;
  }

  async updateAssembly(id: string, updates: Partial<Assembly>): Promise<Assembly> {
    const assembly = await this.getAssemblyById(id);
    if (!assembly) {
      throw new Error(`Assembly with id ${id} not found`);
    }

    const updatedAssembly: Assembly = {
      ...assembly,
      ...updates,
      id: assembly.id,
      version: assembly.version + 1,
      updatedAt: new Date().toISOString(),
      updatedBy: 'current_user'
    };

    console.log('Updating assembly:', updatedAssembly);
    return updatedAssembly;
  }

  async deleteAssembly(id: string): Promise<void> {
    console.log('Deleting assembly:', id);
  }
}

// 产品结构服务
export class ProductStructureService {
  async getStructures(filters?: ProductStructureFilters): Promise<ProductStructure[]> {
    const data = await MockDataLoader.loadMockData<{ productStructures: ProductStructure[] }>('product-structures.json');
    let structures = data.productStructures;

    if (filters) {
      structures = structures.filter(structure => {
        if (filters.productType && structure.productType !== filters.productType) {
          return false;
        }
        if (filters.category && structure.category !== filters.category) {
          return false;
        }
        if (filters.status && structure.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            structure.name.toLowerCase().includes(searchLower) ||
            structure.code.toLowerCase().includes(searchLower) ||
            structure.description?.toLowerCase().includes(searchLower)
          );
        }
        if (filters.tags && filters.tags.length > 0) {
          return filters.tags.some(tag => structure.tags.includes(tag));
        }
        return true;
      });
    }

    return structures;
  }

  async getStructureById(id: string): Promise<ProductStructure | null> {
    const structures = await this.getStructures();
    return structures.find(s => s.id === id) || null;
  }
}

// 产品服务
export class ProductService {
  async getProducts(filters?: ProductFilters): Promise<Product[]> {
    const data = await MockDataLoader.loadMockData<{ products: Product[] }>('products.json');
    let products = data.products;

    if (filters) {
      products = products.filter(product => {
        if (filters.productStructureId && product.productStructureId !== filters.productStructureId) {
          return false;
        }
        if (filters.category && product.category !== filters.category) {
          return false;
        }
        if (filters.lifecycle && product.lifecycle !== filters.lifecycle) {
          return false;
        }
        if (filters.status && product.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            product.name.toLowerCase().includes(searchLower) ||
            product.code.toLowerCase().includes(searchLower) ||
            product.description?.toLowerCase().includes(searchLower)
          );
        }
        if (filters.tags && filters.tags.length > 0) {
          return filters.tags.some(tag => product.tags.includes(tag));
        }
        return true;
      });
    }

    return products;
  }

  async getProductById(id: string): Promise<Product | null> {
    const products = await this.getProducts();
    return products.find(p => p.id === id) || null;
  }

  async createProduct(product: Partial<Product>): Promise<Product> {
    const newProduct: Product = {
      id: `prod_${Date.now()}`,
      code: product.code || '',
      name: product.name || '',
      description: product.description,
      productStructureId: product.productStructureId || '',
      productStructureCode: product.productStructureCode || '',
      productStructureName: product.productStructureName || '',
      productStructureVersion: product.productStructureVersion || 1,
      category: product.category || '',
      subCategory: product.subCategory,
      lifecycle: product.lifecycle || 'design',
      componentMaterialMap: product.componentMaterialMap || [],
      defaultParameters: product.defaultParameters || {},
      status: product.status || 'draft',
      tags: product.tags || [],
      documents: product.documents || [],
      statistics: product.statistics || {
        quoteBOMCount: 0,
        productionBOMCount: 0,
        orderCount: 0
      },
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Creating product:', newProduct);
    return newProduct;
  }
}

// 报价BOM服务
export class QuoteBOMService {
  async getQuoteBOMs(filters?: QuoteBOMFilters): Promise<QuoteBOM[]> {
    const data = await MockDataLoader.loadMockData<{ quoteBOMs: QuoteBOM[] }>('quote-boms.json');
    let boms = data.quoteBOMs;

    if (filters) {
      boms = boms.filter(bom => {
        if (filters.productId && bom.productId !== filters.productId) {
          return false;
        }
        if (filters.status && bom.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            bom.name.toLowerCase().includes(searchLower) ||
            bom.code.toLowerCase().includes(searchLower) ||
            bom.productName.toLowerCase().includes(searchLower)
          );
        }
        if (filters.costRange) {
          const [minCost, maxCost] = filters.costRange;
          return bom.costSummary.totalCost >= minCost && bom.costSummary.totalCost <= maxCost;
        }
        return true;
      });
    }

    return boms;
  }

  async getQuoteBOMById(id: string): Promise<QuoteBOM | null> {
    const boms = await this.getQuoteBOMs();
    return boms.find(b => b.id === id) || null;
  }

  async generateQuoteBOM(productId: string, configuration: ProductConfiguration): Promise<QuoteBOM> {
    const product = await productService.getProductById(productId);
    if (!product) {
      throw new Error(`Product with id ${productId} not found`);
    }

    // 简化的BOM生成逻辑
    const bomItems = await this.generateBOMItems(product, configuration);
    const costSummary = this.calculateCostSummary(bomItems);

    const quoteBOM: QuoteBOM = {
      id: `qbom_${Date.now()}`,
      code: `QB-${Date.now()}`,
      name: `${product.name}-报价BOM`,
      productId: product.id,
      productCode: product.code,
      productName: product.name,
      productVersion: product.version,
      configurationSnapshot: configuration.parameterValues,
      items: bomItems,
      costSummary,
      status: 'draft',
      validFrom: new Date().toISOString(),
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Generated quote BOM:', quoteBOM);
    return quoteBOM;
  }

  private async generateBOMItems(product: Product, configuration: ProductConfiguration) {
    // 简化的BOM项目生成逻辑
    const items = [];

    for (const mapping of product.componentMaterialMap) {
      const component = await componentService.getComponentById(mapping.componentId);
      if (!component) continue;

      const quantity = await componentService.calculateQuantity(
        component.id,
        configuration.parameterValues
      );

      items.push({
        id: `qitem_${Date.now()}_${Math.random()}`,
        level: 1,
        componentId: component.id,
        componentCode: component.code,
        componentName: component.name,
        materialCategoryId: mapping.materialCategoryId,
        materialCategoryName: mapping.materialCategoryName,
        quantity,
        unit: 'pcs',
        unitCost: 100, // 简化的成本计算
        totalCost: quantity * 100,
        wastageRate: 5,
        actualQuantity: quantity * 1.05,
        optional: false
      });
    }

    return items;
  }

  private calculateCostSummary(items: any[]) {
    const materialCost = items.reduce((sum, item) => sum + item.totalCost, 0);
    const laborCost = materialCost * 0.3;
    const overheadCost = materialCost * 0.13;
    const totalCost = materialCost + laborCost + overheadCost;

    return {
      materialCost,
      laborCost,
      overheadCost,
      totalCost
    };
  }
}

// 生产BOM服务
export class ProductionBOMService {
  async getProductionBOMs(filters?: ProductionBOMFilters): Promise<ProductionBOM[]> {
    const data = await MockDataLoader.loadMockData<{ productionBOMs: ProductionBOM[] }>('production-boms.json');
    let boms = data.productionBOMs;

    if (filters) {
      boms = boms.filter(bom => {
        if (filters.productId && bom.productId !== filters.productId) {
          return false;
        }
        if (filters.quoteBOMId && bom.quoteBOMId !== filters.quoteBOMId) {
          return false;
        }
        if (filters.status && bom.status !== filters.status) {
          return false;
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            bom.name.toLowerCase().includes(searchLower) ||
            bom.code.toLowerCase().includes(searchLower) ||
            bom.productName.toLowerCase().includes(searchLower)
          );
        }
        return true;
      });
    }

    return boms;
  }

  async getProductionBOMById(id: string): Promise<ProductionBOM | null> {
    const boms = await this.getProductionBOMs();
    return boms.find(b => b.id === id) || null;
  }

  async convertFromQuoteBOM(quoteBOMId: string): Promise<ProductionBOM> {
    const quoteBOM = await quoteBOMService.getQuoteBOMById(quoteBOMId);
    if (!quoteBOM) {
      throw new Error(`Quote BOM with id ${quoteBOMId} not found`);
    }

    // 简化的转换逻辑
    const productionItems = quoteBOM.items.map(quoteItem => ({
      ...quoteItem,
      id: `pitem_${Date.now()}_${Math.random()}`,
      materialVariantId: `mv_${Math.random()}`,
      materialVariantCode: `VARIANT_${quoteItem.componentCode}`,
      materialVariantName: `${quoteItem.componentName} - 具体变体`,
      alternativeMaterials: [],
      stockInfo: {
        availableQuantity: 100,
        reservedQuantity: 0,
        shortageQuantity: 0
      },
      procurementInfo: {
        leadTime: 7,
        minOrderQuantity: 1,
        supplierCode: 'SUP_001'
      }
    }));

    const productionBOM: ProductionBOM = {
      id: `pbom_${Date.now()}`,
      code: `PB-${Date.now()}`,
      name: `${quoteBOM.productName}-生产BOM`,
      productId: quoteBOM.productId,
      productCode: quoteBOM.productCode,
      productName: quoteBOM.productName,
      productVersion: quoteBOM.productVersion,
      quoteBOMId: quoteBOM.id,
      quoteBOMCode: quoteBOM.code,
      quoteBOMVersion: quoteBOM.version,
      configurationSnapshot: quoteBOM.configurationSnapshot,
      items: productionItems,
      costSummary: quoteBOM.costSummary,
      status: 'draft',
      effectiveFrom: new Date().toISOString(),
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current_user',
      updatedBy: 'current_user'
    };

    console.log('Converted to production BOM:', productionBOM);
    return productionBOM;
  }
}

// 导出服务实例
export const componentService = new ComponentService();
export const assemblyService = new AssemblyService();
export const productStructureService = new ProductStructureService();
export const productService = new ProductService();
export const quoteBOMService = new QuoteBOMService();
export const productionBOMService = new ProductionBOMService();
