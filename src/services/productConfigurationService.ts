import type { 
  ProductConfiguration, 
  ProductStructureTemplate, 
  ProductSearchFilters, 
  ProductUsageStatistics,
  ConfigurationRequest
} from '@/types/product';

// 产品配置服务
export class ProductConfigurationService {
  private baseUrl = '/mock/product';

  // 获取产品配置列表
  async getProductConfigurations(
    filters?: ProductSearchFilters,
    page = 1,
    pageSize = 10
  ): Promise<{
    data: ProductConfiguration[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/product-configurations.json`);
      const result = await response.json();
      let configurations = result.productConfigurations as ProductConfiguration[];

      // 应用筛选条件
      if (filters) {
        configurations = this.applyFilters(configurations, filters);
      }

      // 分页处理
      const total = configurations.length;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = configurations.slice(startIndex, endIndex);

      return {
        data: paginatedData,
        total,
        page,
        pageSize
      };
    } catch (error) {
      console.error('获取产品配置列表失败:', error);
      throw new Error('获取产品配置列表失败');
    }
  }

  // 根据ID获取产品配置详情
  async getProductConfigurationById(id: string): Promise<ProductConfiguration | null> {
    try {
      const response = await fetch(`${this.baseUrl}/product-configurations.json`);
      const result = await response.json();
      const configurations = result.productConfigurations as ProductConfiguration[];
      
      return configurations.find(config => config.id === id) || null;
    } catch (error) {
      console.error('获取产品配置详情失败:', error);
      throw new Error('获取产品配置详情失败');
    }
  }

  // 获取产品结构模板列表
  async getProductStructureTemplates(): Promise<ProductStructureTemplate[]> {
    try {
      const response = await fetch(`${this.baseUrl}/product-configurations.json`);
      const result = await response.json();
      return result.productStructureTemplates as ProductStructureTemplate[];
    } catch (error) {
      console.error('获取产品结构模板失败:', error);
      throw new Error('获取产品结构模板失败');
    }
  }

  // 获取产品使用统计
  async getProductUsageStatistics(): Promise<ProductUsageStatistics[]> {
    try {
      const response = await fetch(`${this.baseUrl}/product-configurations.json`);
      const result = await response.json();
      return result.usageStatistics as ProductUsageStatistics[];
    } catch (error) {
      console.error('获取产品使用统计失败:', error);
      throw new Error('获取产品使用统计失败');
    }
  }

  // 搜索产品配置
  async searchProductConfigurations(keyword: string): Promise<ProductConfiguration[]> {
    try {
      const response = await fetch(`${this.baseUrl}/product-configurations.json`);
      const result = await response.json();
      const configurations = result.productConfigurations as ProductConfiguration[];

      if (!keyword.trim()) {
        return configurations;
      }

      const lowerKeyword = keyword.toLowerCase();
      return configurations.filter(config => 
        config.name.toLowerCase().includes(lowerKeyword) ||
        config.description.toLowerCase().includes(lowerKeyword) ||
        config.tags.some(tag => tag.toLowerCase().includes(lowerKeyword)) ||
        config.category.toLowerCase().includes(lowerKeyword)
      );
    } catch (error) {
      console.error('搜索产品配置失败:', error);
      throw new Error('搜索产品配置失败');
    }
  }

  // 创建产品配置
  async createProductConfiguration(config: Omit<ProductConfiguration, 'id' | 'version' | 'createdAt' | 'updatedAt'>): Promise<ProductConfiguration> {
    // 模拟创建操作
    const newConfig: ProductConfiguration = {
      ...config,
      id: `config_${Date.now()}`,
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      usageCount: 0,
      averageCost: this.calculateAverageCost(config.materialComponents)
    };

    console.log('创建产品配置:', newConfig);
    return newConfig;
  }

  // 更新产品配置
  async updateProductConfiguration(id: string, updates: Partial<ProductConfiguration>): Promise<ProductConfiguration> {
    // 模拟更新操作
    const existingConfig = await this.getProductConfigurationById(id);
    if (!existingConfig) {
      throw new Error('产品配置不存在');
    }

    const updatedConfig: ProductConfiguration = {
      ...existingConfig,
      ...updates,
      version: existingConfig.version + 1,
      updatedAt: new Date().toISOString()
    };

    console.log('更新产品配置:', updatedConfig);
    return updatedConfig;
  }

  // 删除产品配置
  async deleteProductConfiguration(id: string): Promise<boolean> {
    // 模拟删除操作
    console.log('删除产品配置:', id);
    return true;
  }

  // 获取热门产品配置
  async getPopularConfigurations(limit = 5): Promise<ProductConfiguration[]> {
    try {
      const response = await fetch(`${this.baseUrl}/product-configurations.json`);
      const result = await response.json();
      const configurations = result.productConfigurations as ProductConfiguration[];

      return configurations
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, limit);
    } catch (error) {
      console.error('获取热门产品配置失败:', error);
      throw new Error('获取热门产品配置失败');
    }
  }

  // 获取最近使用的产品配置
  async getRecentConfigurations(limit = 5): Promise<ProductConfiguration[]> {
    try {
      const response = await fetch(`${this.baseUrl}/product-configurations.json`);
      const result = await response.json();
      const configurations = result.productConfigurations as ProductConfiguration[];

      return configurations
        .filter(config => config.lastUsedAt)
        .sort((a, b) => new Date(b.lastUsedAt!).getTime() - new Date(a.lastUsedAt!).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('获取最近使用产品配置失败:', error);
      throw new Error('获取最近使用产品配置失败');
    }
  }

  // 应用筛选条件
  private applyFilters(configurations: ProductConfiguration[], filters: ProductSearchFilters): ProductConfiguration[] {
    let filtered = [...configurations];

    // 关键字搜索
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      filtered = filtered.filter(config =>
        config.name.toLowerCase().includes(keyword) ||
        config.description.toLowerCase().includes(keyword) ||
        config.tags.some(tag => tag.toLowerCase().includes(keyword))
      );
    }

    // 状态筛选
    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(config => filters.status!.includes(config.status));
    }

    // 结构模板筛选
    if (filters.structureTemplate && filters.structureTemplate.length > 0) {
      filtered = filtered.filter(config => 
        filters.structureTemplate!.includes(config.structureTemplateName)
      );
    }

    // 分类筛选
    if (filters.category && filters.category.length > 0) {
      filtered = filtered.filter(config => filters.category!.includes(config.category));
    }

    // 标签筛选
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(config =>
        filters.tags!.some(tag => config.tags.includes(tag))
      );
    }

    // 使用次数范围筛选
    if (filters.usageCountRange) {
      const [min, max] = filters.usageCountRange;
      filtered = filtered.filter(config => 
        config.usageCount >= min && config.usageCount <= max
      );
    }

    // 成本范围筛选
    if (filters.costRange) {
      const [min, max] = filters.costRange;
      filtered = filtered.filter(config => 
        config.averageCost >= min && config.averageCost <= max
      );
    }

    // 日期范围筛选
    if (filters.dateRange) {
      const [startDate, endDate] = filters.dateRange;
      filtered = filtered.filter(config => {
        const configDate = new Date(config.updatedAt);
        return configDate >= new Date(startDate) && configDate <= new Date(endDate);
      });
    }

    return filtered;
  }

  // 计算平均成本
  private calculateAverageCost(materialComponents: any[]): number {
    return materialComponents.reduce((total, component) => {
      return total + (component.unitCost || 0);
    }, 0);
  }
}

// 导出服务实例
export const productConfigurationService = new ProductConfigurationService();