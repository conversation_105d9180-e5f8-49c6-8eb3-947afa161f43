/**
 * MES制造执行系统数据服务层
 * 提供订单、排版、生产、质量等业务数据的CRUD操作
 */

import type {
  ValidationOrder,
  ValidationOrderItem,
  CustomerOrder,
  ProductionOrder,
  CuttingTask,
  ProductionBatch,
  QualityRecord,
  OptimizationResult,
  WorkstationSchedule,
  SemiFinishedProduct,
} from '@/types/mes-validation'

/**
 * MES数据服务类
 */
export class MESService {
  private baseUrl = '/mock/mes'

  // ==================== 客户订单管理 ====================

  /**
   * 获取所有客户订单
   */
  async getCustomerOrders(): Promise<CustomerOrder[]> {
    try {
      const response = await fetch('/mock/mes/customer-orders.json')
      const data = await response.json()
      return data.orders || []
    } catch (error) {
      console.error('获取客户订单列表失败:', error)
      // 如果新数据文件不存在，回退到旧数据
      try {
        const fallbackResponse = await fetch(`${this.baseUrl}/validation/cutting-optimization/real-orders.json`)
        const fallbackData = await fallbackResponse.json()
        return fallbackData.orders.map((order: any) => ({
          ...order,
          id: order.id.replace('ORD-', 'CO-'),
          orderType: '幕墙工程',
          priority: 'normal',
          items: order.items.map((item: any) => ({
            ...item,
            customerOrderId: item.orderId,
            unitPrice: 200,
            totalAmount: item.quantity * 200,
            deliveryDate: order.requiredDate,
          })),
          status: this.getRandomCustomerOrderStatus(),
          productionOrders: [`WO-${order.id}`],
        }))
      } catch (fallbackError) {
        console.error('回退数据也加载失败:', fallbackError)
        return []
      }
    }
  }

  /**
   * 获取所有订单（保持向后兼容）
   */
  async getOrders(): Promise<ValidationOrder[]> {
    const customerOrders = await this.getCustomerOrders()
    return customerOrders.map(order => ({
      ...order,
      items: order.items.map(item => ({
        ...item,
        orderId: item.customerOrderId,
        processFlow: (item as any).processFlow || this.generateProcessFlow(item.specifications),
        currentStatus: (item as any).currentStatus || '待转工单',
        utilizationRate: 0,
      }))
    }))
  }

  // ==================== 生产工单管理 ====================

  /**
   * 获取所有生产工单
   */
  async getProductionOrders(): Promise<ProductionOrder[]> {
    try {
      const customerOrders = await this.getCustomerOrders()
      
      // 基于客户订单生成生产工单
      return customerOrders.map((customerOrder, index) => ({
        id: `WO-${customerOrder.id}`,
        workOrderNumber: `WO-${new Date().getFullYear()}-${String(index + 1).padStart(4, '0')}`,
        customerOrderId: customerOrder.id,
        customerOrderNumber: customerOrder.orderNumber,
        customerName: customerOrder.customerName,
        items: customerOrder.items.map(item => ({
          id: `WOI-${item.id}`,
          productionOrderId: `WO-${customerOrder.id}`,
          customerOrderItemId: item.id,
          specifications: item.specifications,
          quantity: item.quantity,
          processFlow: this.generateProcessFlow(item.specifications),
          currentStatus: '待排版',
          currentWorkstation: '排版工段',
        })),
        priority: this.getRandomPriority(),
        status: this.getRandomProductionOrderStatus(),
        plannedStartDate: new Date().toISOString(),
        plannedEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        cuttingTasks: [`CT-${customerOrder.id}`], // 模拟关联的排版任务
      }))
    } catch (error) {
      console.error('获取生产工单列表失败:', error)
      return []
    }
  }

  /**
   * 创建生产工单
   */
  async createProductionOrder(config: {
    workOrderNumber: string
    items: any[]
    priority: string
    plannedStartDate: string
    batchConfig?: any
  }): Promise<ProductionOrder | null> {
    try {
      const productionOrder: ProductionOrder = {
        id: `WO-${Date.now()}`,
        workOrderNumber: config.workOrderNumber,
        customerOrderId: config.items[0]?.customerOrderId || '',
        customerOrderNumber: config.items[0]?.orderNumber || '',
        customerName: config.items[0]?.customerName || '',
        items: config.items.map((item, index) => ({
          id: `WOI-${Date.now()}-${index}`,
          productionOrderId: `WO-${Date.now()}`,
          customerOrderItemId: item.id,
          specifications: item.specifications,
          quantity: item.quantity,
          processFlow: item.processFlow || this.generateProcessFlow(item.specifications),
          currentStatus: '待排版',
          currentWorkstation: '排版工段',
        })),
        priority: config.priority as any,
        status: 'pending',
        plannedStartDate: config.plannedStartDate,
        plannedEndDate: new Date(new Date(config.plannedStartDate).getTime() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        cuttingTasks: []
      }

      console.log('创建生产工单:', productionOrder)
      return productionOrder
    } catch (error) {
      console.error('创建生产工单失败:', error)
      return null
    }
  }

  /**
   * 根据客户订单创建生产工单
   */
  async createProductionOrderFromCustomerOrder(customerOrderId: string): Promise<ProductionOrder | null> {
    try {
      const customerOrders = await this.getCustomerOrders()
      const customerOrder = customerOrders.find(order => order.id === customerOrderId)
      
      if (!customerOrder) {
        throw new Error('客户订单不存在')
      }

      const productionOrder: ProductionOrder = {
        id: `WO-${Date.now()}`,
        workOrderNumber: this.generateWorkOrderNumber(),
        customerOrderId: customerOrder.id,
        customerOrderNumber: customerOrder.orderNumber,
        customerName: customerOrder.customerName,
        items: customerOrder.items.map(item => ({
          id: `WOI-${Date.now()}-${item.id}`,
          productionOrderId: `WO-${Date.now()}`,
          customerOrderItemId: item.id,
          specifications: item.specifications,
          quantity: item.quantity,
          processFlow: this.generateProcessFlow(item.specifications),
          currentStatus: '待排版',
          currentWorkstation: '排版工段',
        })),
        priority: 'normal',
        status: 'pending',
        plannedStartDate: new Date().toISOString(),
        plannedEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      console.log('创建生产工单:', productionOrder)
      return productionOrder
    } catch (error) {
      console.error('创建生产工单失败:', error)
      return null
    }
  }

  /**
   * 更新生产工单状态
   */
  async updateProductionOrderStatus(workOrderId: string, status: string): Promise<boolean> {
    try {
      console.log(`更新生产工单 ${workOrderId} 状态为: ${status}`)
      return true
    } catch (error) {
      console.error('更新生产工单状态失败:', error)
      return false
    }
  }

  /**
   * 根据ID获取订单详情
   */
  async getOrderById(orderId: string): Promise<ValidationOrder | null> {
    const orders = await this.getOrders()
    return orders.find(order => order.id === orderId) || null
  }

  /**
   * 创建新订单
   */
  async createOrder(orderData: Partial<ValidationOrder>): Promise<ValidationOrder> {
    const newOrder: ValidationOrder = {
      id: `order-${Date.now()}`,
      orderNumber: orderData.orderNumber || this.generateOrderNumber(),
      customerName: orderData.customerName || '',
      items: orderData.items || [],
      requiredDate: orderData.requiredDate || new Date().toISOString(),
      estimatedCost: orderData.estimatedCost || 0,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    // 模拟保存到后端
    console.log('创建订单:', newOrder)
    return newOrder
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(orderId: string, status: string): Promise<boolean> {
    try {
      // 模拟API调用
      console.log(`更新订单 ${orderId} 状态为: ${status}`)
      return true
    } catch (error) {
      console.error('更新订单状态失败:', error)
      return false
    }
  }

  /**
   * 删除订单
   */
  async deleteOrder(orderId: string): Promise<boolean> {
    try {
      // 模拟API调用
      console.log(`删除订单: ${orderId}`)
      return true
    } catch (error) {
      console.error('删除订单失败:', error)
      return false
    }
  }

  // ==================== 排版任务管理 ====================

  /**
   * 获取排版任务列表
   */
  async getCuttingTasks(): Promise<CuttingTask[]> {
    try {
      const response = await fetch(`${this.baseUrl}/validation/cutting-optimization/optimized-results.json`)
      const data = await response.json()
      
      // 模拟排版任务数据
      return [
        {
          id: 'CT-001',
          taskName: '华润置地-排版任务',
          glassType: {
            material: 'Low-E玻璃',
            thickness: 6,
            color: '透明'
          },
          orderItems: [],
          rawSheets: [],
          status: 'completed',
          createdAt: new Date().toISOString(),
          optimizationResults: data.results || []
        }
      ]
    } catch (error) {
      console.error('获取排版任务失败:', error)
      return []
    }
  }

  /**
   * 创建排版任务
   */
  async createCuttingTask(orderItems: ValidationOrderItem[]): Promise<CuttingTask> {
    const task: CuttingTask = {
      id: `CT-${Date.now()}`,
      taskName: `排版任务-${new Date().toLocaleDateString()}`,
      glassType: {
        material: orderItems[0]?.specifications.glassType || 'clear',
        thickness: orderItems[0]?.specifications.thickness || 6,
        color: orderItems[0]?.specifications.color || '透明'
      },
      orderItems,
      rawSheets: [],
      status: 'pending',
      createdAt: new Date().toISOString(),
    }

    console.log('创建排版任务:', task)
    return task
  }

  /**
   * 运行排版优化
   */
  async runOptimization(taskId: string, algorithm: string): Promise<OptimizationResult[]> {
    try {
      const response = await fetch(`${this.baseUrl}/validation/cutting-optimization/optimized-results.json`)
      const data = await response.json()
      
      console.log(`运行排版优化 - 任务ID: ${taskId}, 算法: ${algorithm}`)
      return data.results || []
    } catch (error) {
      console.error('运行排版优化失败:', error)
      return []
    }
  }

  // ==================== 生产批次管理 ====================

  /**
   * 获取生产批次列表
   */
  async getProductionBatches(): Promise<ProductionBatch[]> {
    try {
      const response = await fetch(`${this.baseUrl}/validation/workstation-scheduling/production-scenarios.json`)
      const data = await response.json()
      
      // 模拟生产批次数据
      return data.scenarios?.map((scenario: any, index: number) => ({
        id: `PB-${index + 1}`,
        batchNumber: `BATCH-${scenario.name}`,
        workstation: scenario.workstation || '钢化工段',
        processType: scenario.processType || '钢化',
        orderItems: [],
        status: this.getRandomBatchStatus(),
        plannedStartTime: new Date().toISOString(),
        plannedEndTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        equipment: scenario.equipment || '钢化炉#1',
        processParameters: scenario.parameters || {},
        qualityRecords: [],
      })) || []
    } catch (error) {
      console.error('获取生产批次失败:', error)
      return []
    }
  }

  /**
   * 创建生产批次
   */
  async createProductionBatch(batchData: Partial<ProductionBatch>): Promise<ProductionBatch> {
    const batch: ProductionBatch = {
      id: `PB-${Date.now()}`,
      batchNumber: batchData.batchNumber || this.generateBatchNumber(),
      workstation: batchData.workstation || '',
      processType: batchData.processType || '',
      orderItems: batchData.orderItems || [],
      status: 'planned',
      plannedStartTime: batchData.plannedStartTime || new Date().toISOString(),
      plannedEndTime: batchData.plannedEndTime || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      equipment: batchData.equipment || '',
      processParameters: batchData.processParameters || {},
      qualityRecords: [],
    }

    console.log('创建生产批次:', batch)
    return batch
  }

  /**
   * 更新批次状态
   */
  async updateBatchStatus(batchId: string, status: string): Promise<boolean> {
    try {
      console.log(`更新批次 ${batchId} 状态为: ${status}`)
      return true
    } catch (error) {
      console.error('更新批次状态失败:', error)
      return false
    }
  }

  // ==================== 工段调度管理 ====================

  /**
   * 获取工段调度信息
   */
  async getWorkstationSchedules(): Promise<WorkstationSchedule[]> {
    try {
      const response = await fetch(`${this.baseUrl}/validation/workstation-scheduling/optimized-flow.json`)
      const data = await response.json()
      
      // 模拟工段调度数据
      return data.workstations?.map((ws: any) => ({
        id: ws.id,
        workstationId: ws.workstationId,
        workstationName: ws.name,
        date: new Date().toISOString().split('T')[0],
        shifts: ws.shifts || [],
        capacity: ws.capacity || {
          maxBatches: 10,
          maxPieces: 100,
          maxArea: 1000
        },
        utilization: ws.utilization || {
          plannedBatches: 0,
          plannedPieces: 0,
          plannedArea: 0,
          utilizationRate: 0
        },
        efficiency: ws.efficiency
      })) || []
    } catch (error) {
      console.error('获取工段调度失败:', error)
      return []
    }
  }

  // ==================== 半成品管理 ====================

  /**
   * 获取半成品库存
   */
  async getSemiFinishedProducts(): Promise<SemiFinishedProduct[]> {
    // 模拟半成品数据
    return [
      {
        id: 'SF-001',
        orderItemId: 'ITEM-001',
        currentWorkstation: '钢化工段',
        specifications: {
          length: 1800,
          width: 1200,
          thickness: 6,
          glassType: 'low_e'
        },
        quantity: 50,
        status: 'available',
        location: 'A区-01架',
        qualityStatus: 'passed',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    ]
  }

  /**
   * 转移半成品
   */
  async transferSemiFinished(productId: string, targetWorkstation: string): Promise<boolean> {
    try {
      console.log(`转移半成品 ${productId} 到工段: ${targetWorkstation}`)
      return true
    } catch (error) {
      console.error('转移半成品失败:', error)
      return false
    }
  }

  // ==================== 质量管理 ====================

  /**
   * 获取质量记录
   */
  async getQualityRecords(): Promise<QualityRecord[]> {
    // 模拟质量记录数据
    return [
      {
        id: 'QR-001',
        orderItemId: 'ITEM-001',
        batchId: 'PB-001',
        processType: '钢化',
        inspectionType: 'in_process',
        inspectionItems: [
          {
            itemName: '平整度',
            standard: '≤0.3mm/m',
            actualValue: '0.2mm/m',
            result: 'pass',
            tolerance: '±0.1mm/m'
          }
        ],
        inspector: '张师傅',
        inspectionDate: new Date().toISOString(),
        result: 'passed',
      }
    ]
  }

  /**
   * 创建质量记录
   */
  async createQualityRecord(recordData: Partial<QualityRecord>): Promise<QualityRecord> {
    const record: QualityRecord = {
      id: `QR-${Date.now()}`,
      orderItemId: recordData.orderItemId || '',
      batchId: recordData.batchId || '',
      processType: recordData.processType || '',
      inspectionType: recordData.inspectionType || 'in_process',
      inspectionItems: recordData.inspectionItems || [],
      inspector: recordData.inspector || '',
      inspectionDate: new Date().toISOString(),
      result: recordData.result || 'passed',
      defects: recordData.defects,
      notes: recordData.notes,
    }

    console.log('创建质量记录:', record)
    return record
  }

  // ==================== 交期管理 ====================

  /**
   * 计算交期承诺
   */
  async calculateDeliveryPromise(orderItems: ValidationOrderItem[]): Promise<{
    promisedDate: string;
    confidence: number;
    breakdown: any;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/validation/delivery-promise/smart-promises.json`)
      const data = await response.json()
      
      // 模拟交期计算
      const totalDays = orderItems.reduce((sum, item) => {
        return sum + item.processFlow.reduce((flowSum, step) => {
          return flowSum + (step.estimatedDuration / 60 / 8) // 转换为天数
        }, 0)
      }, 0)

      const promisedDate = new Date(Date.now() + totalDays * 24 * 60 * 60 * 1000).toISOString()
      
      return {
        promisedDate,
        confidence: data.averageConfidence || 85,
        breakdown: {
          queueTime: Math.ceil(totalDays * 0.3),
          productionTime: Math.ceil(totalDays * 0.6),
          qualityTime: Math.ceil(totalDays * 0.1)
        }
      }
    } catch (error) {
      console.error('计算交期承诺失败:', error)
      return {
        promisedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        confidence: 70,
        breakdown: {
          queueTime: 2,
          productionTime: 4,
          qualityTime: 1
        }
      }
    }
  }

  // ==================== 数据关联查询 ====================

  /**
   * 根据订单ID获取关联的排版任务
   */
  async getCuttingTasksByOrderId(orderId: string): Promise<CuttingTask[]> {
    const tasks = await this.getCuttingTasks()
    return tasks.filter(task => 
      task.orderItems.some(item => item.orderId === orderId)
    )
  }

  /**
   * 根据订单ID获取关联的生产批次
   */
  async getProductionBatchesByOrderId(orderId: string): Promise<ProductionBatch[]> {
    const batches = await this.getProductionBatches()
    return batches.filter(batch => 
      batch.orderItems.some(item => item.orderId === orderId)
    )
  }

  /**
   * 根据订单ID获取关联的质量记录
   */
  async getQualityRecordsByOrderId(orderId: string): Promise<QualityRecord[]> {
    const records = await this.getQualityRecords()
    return records.filter(record => 
      record.orderItemId.startsWith(orderId) // 简化的关联逻辑
    )
  }

  // ==================== 工具方法 ====================

  /**
   * 生成订单号
   */
  private generateOrderNumber(): string {
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')
    const day = String(new Date().getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `ORD-${year}${month}${day}-${random}`
  }

  /**
   * 生成批次号
   */
  private generateBatchNumber(): string {
    const date = new Date().toISOString().split('T')[0].replace(/-/g, '')
    const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')
    return `BATCH-${date}-${random}`
  }

  /**
   * 生成工单号
   */
  private generateWorkOrderNumber(): string {
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')
    const day = String(new Date().getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `WO-${year}${month}${day}-${random}`
  }

  /**
   * 生成工艺流程
   */
  private generateProcessFlow(specifications: unknown): unknown[] {
    const flow = []

    // 冷工段工艺（必须在钢化前完成）
    flow.push({
      stepName: '切割',
      workstation: 'cold_processing',
      estimatedDuration: 15,
      constraints: {
        bladeWidth: 3.2,
        edgeMargin: 20
      },
      status: 'pending',
      workstationGroup: '冷工段'
    })

    flow.push({
      stepName: '磨边',
      workstation: 'cold_processing',
      estimatedDuration: 25,
      constraints: {
        edgeType: 'polished'
      },
      status: 'pending',
      workstationGroup: '冷工段'
    })

    // 根据玻璃类型添加镀膜工艺（冷工段）
    if (specifications.glassType === 'low_e' || specifications.glassType === 'reflective') {
      flow.push({
        stepName: '镀膜',
        workstation: 'cold_processing',
        estimatedDuration: 30,
        constraints: {
          coatingType: specifications.glassType,
          temperature: 200
        },
        status: 'pending',
        workstationGroup: '冷工段'
      })
    }

    // 钢化工段（如果需要钢化）
    if (specifications.thickness >= 5) {
      flow.push({
        stepName: '钢化',
        workstation: 'tempering',
        estimatedDuration: 45,
        constraints: {
          temperature: specifications.thickness <= 6 ? 675 : 680,
          coolingTime: specifications.thickness <= 6 ? 160 : 180
        },
        status: 'pending',
        workstationGroup: '钢化工段'
      })
    }

    return flow
  }

  /**
   * 获取随机优先级
   */
  private getRandomPriority(): 'low' | 'normal' | 'high' | 'urgent' {
    const priorities = ['low', 'normal', 'high', 'urgent']
    return priorities[Math.floor(Math.random() * priorities.length)] as unknown
  }

  /**
   * 获取随机客户订单状态
   */
  private getRandomCustomerOrderStatus(): string {
    const statuses = ['confirmed', 'in_production', 'completed', 'cancelled']
    return statuses[Math.floor(Math.random() * statuses.length)]
  }

  /**
   * 获取随机生产工单状态
   */
  private getRandomProductionOrderStatus(): string {
    const statuses = ['pending', 'released', 'in_progress', 'completed', 'cancelled']
    return statuses[Math.floor(Math.random() * statuses.length)]
  }

  /**
   * 获取随机订单状态（保持向后兼容）
   */
  private getRandomOrderStatus(): string {
    const statuses = ['pending', 'in_progress', 'quality_check', 'completed']
    return statuses[Math.floor(Math.random() * statuses.length)]
  }

  /**
   * 获取随机批次状态
   */
  private getRandomBatchStatus(): string {
    const statuses = ['planned', 'in_progress', 'completed', 'cancelled']
    return statuses[Math.floor(Math.random() * statuses.length)]
  }
}

// 导出单例实例
export const mesService = new MESService()

// 导出默认实例
export default mesService