import type { SelectedOrderItem, BatchOptimizationResult, ProcessStep } from '@/types/production-order-creation'

/**
 * 批次优化服务
 * 负责分析订单项并生成最优的生产批次方案
 */
export class BatchOptimizationService {
  /**
   * 优化批次分组
   * @param selectedOrderItems 已选择的订单项
   * @returns 批次优化结果
   */
  async optimizeBatches(selectedOrderItems: SelectedOrderItem[]): Promise<BatchOptimizationResult> {
    if (selectedOrderItems.length === 0) {
      return this.createEmptyResult()
    }

    // 模拟异步处理
    await new Promise(resolve => setTimeout(resolve, 1000))

    try {
      // 1. 分析工艺兼容性
      const compatibilityGroups = this.analyzeProcessCompatibility(selectedOrderItems)
      
      // 2. 按规格分组
      const specificationGroups = this.groupBySpecifications(selectedOrderItems)
      
      // 3. 按客户分组（可选）
      const customerGroups = this.groupByCustomer(selectedOrderItems)
      
      // 4. 生成最优批次
      const batches = this.generateOptimalBatches(
        selectedOrderItems,
        compatibilityGroups,
        specificationGroups
      )
      
      // 5. 计算优化效果
      const efficiency = this.calculateEfficiencyImprovement(selectedOrderItems, batches)
      const timeSaved = this.calculateTimeSaved(selectedOrderItems, batches)
      
      // 6. 生成优化建议
      const recommendations = this.generateRecommendations(selectedOrderItems, batches)

      return {
        efficiency,
        timeSaved,
        batches,
        recommendations,
        totalItems: selectedOrderItems.length,
        totalQuantity: selectedOrderItems.reduce((sum, item) => sum + item.selectedQuantity, 0),
        compatibilityAnalysis: {
          totalGroups: compatibilityGroups.length,
          conflicts: this.findProcessConflicts(selectedOrderItems),
          suggestions: this.generateCompatibilitySuggestions(compatibilityGroups)
        }
      }
    } catch (error) {
      console.error('批次优化失败:', error)
      return this.createFallbackResult(selectedOrderItems)
    }
  }

  /**
   * 分析工艺兼容性
   */
  private analyzeProcessCompatibility(items: SelectedOrderItem[]): SelectedOrderItem[][] {
    const groups: SelectedOrderItem[][] = []
    const processed = new Set<string>()

    for (const item of items) {
      if (processed.has(item.id)) continue

      const compatibleItems = [item]
      processed.add(item.id)

      // 查找与当前项工艺兼容的其他项
      for (const otherItem of items) {
        if (processed.has(otherItem.id)) continue

        if (this.areProcessesCompatible(item.processFlow, otherItem.processFlow)) {
          compatibleItems.push(otherItem)
          processed.add(otherItem.id)
        }
      }

      groups.push(compatibleItems)
    }

    return groups
  }

  /**
   * 检查两个工艺流程是否兼容
   */
  private areProcessesCompatible(flow1: ProcessStep[], flow2: ProcessStep[]): boolean {
    if (!flow1 || !flow2 || flow1.length === 0 || flow2.length === 0) {
      return false
    }

    // 简化的兼容性检查：工艺步骤数量和主要步骤相同
    if (flow1.length !== flow2.length) {
      return false
    }

    // 检查关键工艺步骤是否匹配
    const keySteps1 = flow1.filter(step => this.isKeyProcessStep(step.stepName))
    const keySteps2 = flow2.filter(step => this.isKeyProcessStep(step.stepName))

    if (keySteps1.length !== keySteps2.length) {
      return false
    }

    return keySteps1.every((step1, index) => 
      keySteps2[index] && step1.stepName === keySteps2[index].stepName
    )
  }

  /**
   * 判断是否为关键工艺步骤
   */
  private isKeyProcessStep(stepName: string): boolean {
    const keySteps = ['tempering', 'laminating', 'insulating', 'coating']
    return keySteps.includes(stepName)
  }

  /**
   * 按规格分组
   */
  private groupBySpecifications(items: SelectedOrderItem[]): Map<string, SelectedOrderItem[]> {
    const groups = new Map<string, SelectedOrderItem[]>()

    for (const item of items) {
      const key = `${item.specifications.length}x${item.specifications.width}x${item.specifications.thickness}_${item.specifications.glassType}`
      
      if (!groups.has(key)) {
        groups.set(key, [])
      }
      groups.get(key)!.push(item)
    }

    return groups
  }

  /**
   * 按客户分组
   */
  private groupByCustomer(items: SelectedOrderItem[]): Map<string, SelectedOrderItem[]> {
    const groups = new Map<string, SelectedOrderItem[]>()

    for (const item of items) {
      if (!groups.has(item.customerName)) {
        groups.set(item.customerName, [])
      }
      groups.get(item.customerName)!.push(item)
    }

    return groups
  }

  /**
   * 生成最优批次
   */
  private generateOptimalBatches(
    items: SelectedOrderItem[],
    compatibilityGroups: SelectedOrderItem[][],
    specificationGroups: Map<string, SelectedOrderItem[]>
  ): any[] {
    const batches: any[] = []

    // 基于兼容性分组创建批次
    for (let i = 0; i < compatibilityGroups.length; i++) {
      const group = compatibilityGroups[i]
      
      // 进一步按规格细分批次
      const specGroups = this.groupBySpecifications(group)
      
      for (const [spec, specItems] of specGroups) {
        const totalQuantity = specItems.reduce((sum, item) => sum + item.selectedQuantity, 0)
        
        // 如果数量太大，拆分为多个批次
        if (totalQuantity > 500) {
          const subBatches = this.splitLargeBatch(specItems)
          batches.push(...subBatches)
        } else {
          batches.push({
            id: `batch_${batches.length + 1}`,
            name: `批次 ${batches.length + 1}`,
            items: specItems,
            specifications: spec,
            totalQuantity,
            estimatedDuration: this.calculateBatchDuration(specItems),
            workstations: this.getRequiredWorkstations(specItems),
            priority: this.calculateBatchPriority(specItems)
          })
        }
      }
    }

    return batches
  }

  /**
   * 拆分大批次
   */
  private splitLargeBatch(items: SelectedOrderItem[]): any[] {
    const batches: any[] = []
    const maxBatchSize = 500
    let currentBatch: SelectedOrderItem[] = []
    let currentQuantity = 0

    for (const item of items) {
      if (currentQuantity + item.selectedQuantity > maxBatchSize && currentBatch.length > 0) {
        // 创建当前批次
        batches.push(this.createBatchFromItems(currentBatch, batches.length + 1))
        currentBatch = []
        currentQuantity = 0
      }

      currentBatch.push(item)
      currentQuantity += item.selectedQuantity
    }

    // 处理最后一个批次
    if (currentBatch.length > 0) {
      batches.push(this.createBatchFromItems(currentBatch, batches.length + 1))
    }

    return batches
  }

  /**
   * 从订单项创建批次
   */
  private createBatchFromItems(items: SelectedOrderItem[], batchNumber: number): any {
    const totalQuantity = items.reduce((sum, item) => sum + item.selectedQuantity, 0)
    const spec = items.length > 0 ? 
      `${items[0].specifications.length}x${items[0].specifications.width}x${items[0].specifications.thickness}` : 
      '混合规格'

    return {
      id: `batch_${batchNumber}`,
      name: `批次 ${batchNumber}`,
      items,
      specifications: spec,
      totalQuantity,
      estimatedDuration: this.calculateBatchDuration(items),
      workstations: this.getRequiredWorkstations(items),
      priority: this.calculateBatchPriority(items)
    }
  }

  /**
   * 计算批次持续时间
   */
  private calculateBatchDuration(items: SelectedOrderItem[]): number {
    if (items.length === 0) return 0

    // 获取最长的工艺流程时间
    const maxProcessTime = Math.max(...items.map(item => 
      item.processFlow.reduce((sum, step) => sum + step.estimatedDuration, 0)
    ))

    // 考虑批次效应：相同工艺的项目可以并行处理
    const batchEfficiency = Math.min(0.8, 1 - (items.length - 1) * 0.05)
    
    return Math.round(maxProcessTime * batchEfficiency)
  }

  /**
   * 获取所需工作站
   */
  private getRequiredWorkstations(items: SelectedOrderItem[]): string[] {
    const workstations = new Set<string>()

    for (const item of items) {
      for (const step of item.processFlow) {
        workstations.add(step.workstation)
      }
    }

    return Array.from(workstations)
  }

  /**
   * 计算批次优先级
   */
  private calculateBatchPriority(items: SelectedOrderItem[]): 'urgent' | 'high' | 'normal' | 'low' {
    // 基于交期和客户重要性计算优先级
    const hasUrgentDelivery = items.some(item => {
      const deliveryDate = new Date(item.deliveryDate)
      const daysUntilDelivery = (deliveryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)
      return daysUntilDelivery <= 3
    })

    if (hasUrgentDelivery) return 'urgent'

    const hasHighPriorityCustomer = items.some(item => 
      ['华润置地', '万科集团', '碧桂园'].includes(item.customerName)
    )

    if (hasHighPriorityCustomer) return 'high'

    return 'normal'
  }

  /**
   * 计算效率提升
   */
  private calculateEfficiencyImprovement(items: SelectedOrderItem[], batches: any[]): number {
    // 单独生产的总时间
    const individualTime = items.reduce((sum, item) => 
      sum + item.processFlow.reduce((stepSum, step) => stepSum + step.estimatedDuration, 0), 0
    )

    // 批次生产的总时间
    const batchTime = batches.reduce((sum, batch) => sum + batch.estimatedDuration, 0)

    // 计算效率提升百分比
    const improvement = ((individualTime - batchTime) / individualTime) * 100
    return Math.max(0, Math.round(improvement))
  }

  /**
   * 计算节省时间
   */
  private calculateTimeSaved(items: SelectedOrderItem[], batches: any[]): number {
    const individualTime = items.reduce((sum, item) => 
      sum + item.processFlow.reduce((stepSum, step) => stepSum + step.estimatedDuration, 0), 0
    )

    const batchTime = batches.reduce((sum, batch) => sum + batch.estimatedDuration, 0)

    return Math.max(0, Math.round((individualTime - batchTime) / 60 * 10) / 10) // 转换为小时
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(items: SelectedOrderItem[], batches: any[]): string[] {
    const recommendations: string[] = []

    // 基于批次数量的建议
    if (batches.length > items.length * 0.8) {
      recommendations.push('建议合并相似规格的产品以减少批次数量')
    }

    // 基于工艺流程的建议
    const hasComplexProcess = items.some(item => item.processFlow.length > 5)
    if (hasComplexProcess) {
      recommendations.push('复杂工艺产品建议安排在生产负荷较低时段')
    }

    // 基于数量的建议
    const totalQuantity = items.reduce((sum, item) => sum + item.selectedQuantity, 0)
    if (totalQuantity > 1000) {
      recommendations.push('大批量生产建议分多个班次完成')
    }

    // 基于客户的建议
    const customerCount = new Set(items.map(item => item.customerName)).size
    if (customerCount > 5) {
      recommendations.push('多客户订单建议按交期优先级排序')
    }

    return recommendations
  }

  /**
   * 查找工艺冲突
   */
  private findProcessConflicts(items: SelectedOrderItem[]): any[] {
    const conflicts: any[] = []

    for (let i = 0; i < items.length; i++) {
      for (let j = i + 1; j < items.length; j++) {
        const item1 = items[i]
        const item2 = items[j]

        if (!this.areProcessesCompatible(item1.processFlow, item2.processFlow)) {
          conflicts.push({
            id: `conflict_${i}_${j}`,
            items: [item1.id, item2.id],
            reason: '工艺流程不兼容',
            severity: 'warning',
            suggestion: '建议分配到不同批次'
          })
        }
      }
    }

    return conflicts
  }

  /**
   * 生成兼容性建议
   */
  private generateCompatibilitySuggestions(groups: SelectedOrderItem[][]): string[] {
    const suggestions: string[] = []

    if (groups.length > 1) {
      suggestions.push(`已识别 ${groups.length} 个工艺兼容组`)
    }

    const largeGroups = groups.filter(group => group.length > 3)
    if (largeGroups.length > 0) {
      suggestions.push('大型兼容组可考虑进一步细分以优化资源利用')
    }

    return suggestions
  }

  /**
   * 创建空结果
   */
  private createEmptyResult(): BatchOptimizationResult {
    return {
      efficiency: 0,
      timeSaved: 0,
      batches: [],
      recommendations: [],
      totalItems: 0,
      totalQuantity: 0
    }
  }

  /**
   * 创建回退结果
   */
  private createFallbackResult(items: SelectedOrderItem[]): BatchOptimizationResult {
    return {
      efficiency: 5,
      timeSaved: 0.5,
      batches: [{
        id: 'batch_1',
        name: '批次 1',
        items,
        specifications: '混合规格',
        totalQuantity: items.reduce((sum, item) => sum + item.selectedQuantity, 0),
        estimatedDuration: 120,
        workstations: ['cutting_station_1', 'edging_station_1'],
        priority: 'normal'
      }],
      recommendations: ['系统自动生成基础批次方案'],
      totalItems: items.length,
      totalQuantity: items.reduce((sum, item) => sum + item.selectedQuantity, 0)
    }
  }
}

// 导出单例实例
export const batchOptimizationService = new BatchOptimizationService()