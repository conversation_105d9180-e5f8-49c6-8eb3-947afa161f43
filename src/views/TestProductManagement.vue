<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">产品库管理测试</h1>
    
    <div class="space-y-6">
      <!-- 快速导航 -->
      <Card>
        <CardContent class="p-4">
          <h2 class="text-lg font-semibold mb-4">功能测试</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button @click="testLoadConfigurations" class="h-20 flex-col">
              <Package class="w-6 h-6 mb-2" />
              加载产品配置
            </Button>
            <Button @click="testSearchConfigurations" variant="outline" class="h-20 flex-col">
              <Search class="w-6 h-6 mb-2" />
              搜索功能测试
            </Button>
            <Button @click="testStatistics" variant="outline" class="h-20 flex-col">
              <BarChart3 class="w-6 h-6 mb-2" />
              统计数据测试
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 测试结果 -->
      <Card v-if="testResults.length > 0">
        <CardHeader>
          <CardTitle>测试结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <div 
              v-for="(result, index) in testResults" 
              :key="index"
              class="p-3 rounded-lg"
              :class="result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'"
            >
              <div class="flex items-center gap-2">
                <CheckCircle v-if="result.success" class="w-4 h-4" />
                <XCircle v-else class="w-4 h-4" />
                <span class="font-medium">{{ result.test }}</span>
              </div>
              <p class="text-sm mt-1">{{ result.message }}</p>
              <pre v-if="result.data" class="text-xs mt-2 bg-white p-2 rounded overflow-x-auto">{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 产品配置预览 -->
      <Card v-if="sampleConfigurations.length > 0">
        <CardHeader>
          <CardTitle>产品配置预览</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div 
              v-for="config in sampleConfigurations" 
              :key="config.id"
              class="border rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <h3 class="font-medium text-sm">{{ config.name }}</h3>
              <p class="text-xs text-gray-600 mt-1">{{ config.description }}</p>
              <div class="flex items-center justify-between mt-3">
                <Badge variant="secondary" class="text-xs">{{ config.category }}</Badge>
                <span class="text-xs text-gray-500">{{ config.usageCount }}次使用</span>
              </div>
              <div class="mt-2">
                <span class="text-sm font-medium">¥{{ config.averageCost.toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 导航到完整页面 -->
      <Card>
        <CardContent class="p-4">
          <div class="text-center">
            <h3 class="text-lg font-semibold mb-2">完整功能体验</h3>
            <p class="text-gray-600 mb-4">访问完整的产品库管理页面体验所有功能</p>
            <Button @click="goToProductManagement" size="lg">
              <Package class="w-4 h-4 mr-2" />
              进入产品库管理
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Badge from '@/components/ui/badge/Badge.vue';
import { 
  Package, 
  Search, 
  BarChart3, 
  CheckCircle, 
  XCircle 
} from 'lucide-vue-next';

import { productConfigurationService } from '@/services/productConfigurationService';
import type { ProductConfiguration } from '@/types/product';

const router = useRouter();

// 测试结果
const testResults = ref<Array<{
  test: string;
  success: boolean;
  message: string;
  data?: any;
}>>([]);

// 示例配置数据
const sampleConfigurations = ref<ProductConfiguration[]>([]);

// 测试加载产品配置
const testLoadConfigurations = async () => {
  try {
    const result = await productConfigurationService.getProductConfigurations();
    sampleConfigurations.value = result.data.slice(0, 6); // 只显示前6个
    
    testResults.value.push({
      test: '加载产品配置',
      success: true,
      message: `成功加载 ${result.data.length} 个产品配置`,
      data: {
        total: result.total,
        loaded: result.data.length,
        sampleNames: result.data.slice(0, 3).map(c => c.name)
      }
    });
  } catch (error) {
    testResults.value.push({
      test: '加载产品配置',
      success: false,
      message: `加载失败: ${error.message}`,
    });
  }
};

// 测试搜索功能
const testSearchConfigurations = async () => {
  try {
    const searchResults = await productConfigurationService.searchProductConfigurations('中空');
    
    testResults.value.push({
      test: '搜索功能测试',
      success: true,
      message: `搜索"中空"找到 ${searchResults.length} 个结果`,
      data: {
        searchKeyword: '中空',
        resultCount: searchResults.length,
        results: searchResults.map(c => c.name)
      }
    });
  } catch (error) {
    testResults.value.push({
      test: '搜索功能测试',
      success: false,
      message: `搜索失败: ${error.message}`,
    });
  }
};

// 测试统计数据
const testStatistics = async () => {
  try {
    const statistics = await productConfigurationService.getProductUsageStatistics();
    
    testResults.value.push({
      test: '统计数据测试',
      success: true,
      message: `获取到 ${statistics.length} 个产品的使用统计`,
      data: {
        totalProducts: statistics.length,
        topUsed: statistics.slice(0, 3).map(s => ({
          name: s.productName,
          usage: s.totalUsage
        }))
      }
    });
  } catch (error) {
    testResults.value.push({
      test: '统计数据测试',
      success: false,
      message: `获取统计失败: ${error.message}`,
    });
  }
};

// 导航到产品管理页面
const goToProductManagement = () => {
  router.push('/product-management');
};

// 初始化时自动运行基础测试
onMounted(() => {
  testLoadConfigurations();
});
</script>

<style scoped>
/* 自定义样式 */
</style>