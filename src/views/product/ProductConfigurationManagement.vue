<template>
  <div class="product-configuration-management">
    <!-- 页面标题和操作栏 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">产品库管理</h1>
        <p class="text-gray-600 mt-1">管理和维护产品配置，查看使用统计和趋势分析</p>
      </div>
      <div class="flex gap-3">
        <Button variant="outline" @click="exportConfigurations">
          <Download class="w-4 h-4 mr-2" />
          导出配置
        </Button>
        <Button @click="showCreateDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          新增配置
        </Button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <Package class="w-5 h-5 text-blue-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">总配置数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalConfigurations }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <TrendingUp class="w-5 h-5 text-green-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">活跃配置</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.activeConfigurations }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <BarChart3 class="w-5 h-5 text-purple-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">本月使用</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.monthlyUsage }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
              <DollarSign class="w-5 h-5 text-orange-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-600">平均成本</p>
              <p class="text-2xl font-bold text-gray-900">¥{{ statistics.averageCost }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 搜索和筛选 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <ProductConfigurationFilters
          v-model:filters="filters"
          v-model:search="searchKeyword"
          :structure-templates="structureTemplates"
          @search="handleSearch"
          @filter="handleFilter"
          @reset="handleResetFilters"
        />
      </CardContent>
    </Card>

    <!-- 产品配置列表 -->
    <Card>
      <CardContent class="p-0">
        <ProductConfigurationTable
          :configurations="configurations"
          :loading="loading"
          :pagination="pagination"
          @edit="handleEdit"
          @delete="handleDelete"
          @view-details="handleViewDetails"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </CardContent>
    </Card>

    <!-- 创建/编辑对话框 -->
    <ProductConfigurationDialog
      v-model:open="showCreateDialog"
      :configuration="editingConfiguration"
      :structure-templates="structureTemplates"
      @save="handleSave"
      @cancel="handleCancel"
    />

    <!-- 详情对话框 -->
    <ProductConfigurationDetailsDialog
      v-model:open="showDetailsDialog"
      :configuration="selectedConfiguration"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Package, 
  Plus, 
  Download, 
  TrendingUp, 
  BarChart3, 
  DollarSign 
} from 'lucide-vue-next';

import ProductConfigurationFilters from '@/components/product/ProductConfigurationFilters.vue';
import ProductConfigurationTable from '@/components/product/ProductConfigurationTable.vue';
import ProductConfigurationDialog from '@/components/product/ProductConfigurationDialog.vue';
import ProductConfigurationDetailsDialog from '@/components/product/ProductConfigurationDetailsDialog.vue';

import { productConfigurationService } from '@/services/productConfigurationService';
import type { 
  ProductConfiguration, 
  ProductStructureTemplate, 
  ProductSearchFilters 
} from '@/types/product';

// 响应式数据
const configurations = ref<ProductConfiguration[]>([]);
const structureTemplates = ref<ProductStructureTemplate[]>([]);
const loading = ref(false);
const searchKeyword = ref('');

// 筛选条件
const filters = reactive<ProductSearchFilters>({
  keyword: '',
  status: [],
  structureTemplate: [],
  category: [],
  tags: []
});

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 对话框状态
const showCreateDialog = ref(false);
const showDetailsDialog = ref(false);
const editingConfiguration = ref<ProductConfiguration | null>(null);
const selectedConfiguration = ref<ProductConfiguration | null>(null);

// 统计信息
const statistics = computed(() => {
  const total = configurations.value.length;
  const active = configurations.value.filter(c => c.status === 'active').length;
  const monthlyUsage = configurations.value.reduce((sum, c) => sum + (c.usageCount || 0), 0);
  const averageCost = configurations.value.length > 0 
    ? Math.round(configurations.value.reduce((sum, c) => sum + c.averageCost, 0) / configurations.value.length)
    : 0;

  return {
    totalConfigurations: total,
    activeConfigurations: active,
    monthlyUsage,
    averageCost
  };
});

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    // 加载产品配置列表
    const result = await productConfigurationService.getProductConfigurations(
      filters,
      pagination.page,
      pagination.pageSize
    );
    
    configurations.value = result.data;
    pagination.total = result.total;

    // 加载结构模板
    structureTemplates.value = await productConfigurationService.getProductStructureTemplates();
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = async () => {
  filters.keyword = searchKeyword.value;
  pagination.page = 1;
  await loadData();
};

// 筛选处理
const handleFilter = async () => {
  pagination.page = 1;
  await loadData();
};

// 重置筛选
const handleResetFilters = async () => {
  Object.assign(filters, {
    keyword: '',
    status: [],
    structureTemplate: [],
    category: [],
    tags: []
  });
  searchKeyword.value = '';
  pagination.page = 1;
  await loadData();
};

// 分页处理
const handlePageChange = async (page: number) => {
  pagination.page = page;
  await loadData();
};

const handlePageSizeChange = async (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  await loadData();
};

// 编辑处理
const handleEdit = (configuration: ProductConfiguration) => {
  editingConfiguration.value = configuration;
  showCreateDialog.value = true;
};

// 删除处理
const handleDelete = async (configuration: ProductConfiguration) => {
  if (confirm(`确定要删除产品配置"${configuration.name}"吗？`)) {
    try {
      await productConfigurationService.deleteProductConfiguration(configuration.id);
      await loadData();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }
};

// 查看详情
const handleViewDetails = (configuration: ProductConfiguration) => {
  selectedConfiguration.value = configuration;
  showDetailsDialog.value = true;
};

// 保存处理
const handleSave = async (configuration: ProductConfiguration) => {
  try {
    if (editingConfiguration.value) {
      // 更新
      await productConfigurationService.updateProductConfiguration(
        configuration.id,
        configuration
      );
    } else {
      // 创建
      await productConfigurationService.createProductConfiguration(configuration);
    }
    
    showCreateDialog.value = false;
    editingConfiguration.value = null;
    await loadData();
  } catch (error) {
    console.error('保存失败:', error);
  }
};

// 取消处理
const handleCancel = () => {
  showCreateDialog.value = false;
  editingConfiguration.value = null;
};

// 导出配置
const exportConfigurations = () => {
  // 模拟导出功能
  console.log('导出产品配置');
};

// 初始化
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.product-configuration-management {
  @apply p-6 max-w-7xl mx-auto;
}
</style>