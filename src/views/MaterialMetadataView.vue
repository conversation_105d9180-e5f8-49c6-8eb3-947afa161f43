<template>
  <div class="space-y-6">
    <!-- 页面标题和操作按钮 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">物料元数据管理</h1>
        <p class="text-muted-foreground">
          管理物料分类、基础属性和库存变体信息
        </p>
      </div>
      <div class="flex items-center space-x-4">
        <Button @click="showImportDialog = true" variant="outline">
          <Upload class="mr-2 h-4 w-4" />
          批量导入
        </Button>
        <Button @click="showExportDialog = true" variant="outline">
          <Download class="mr-2 h-4 w-4" />
          批量导出
        </Button>
        <CategoryOperationButtons
          :selected-category="selectedCategory"
          @add-category="handleAddCategory"
          @edit-category="handleEditCategory"
          @add-child-category="handleAddChildCategory"
          @duplicate-category="handleDuplicateCategory"
          @delete-category="handleDeleteCategory"
          @export-category="handleExportCategory"
          @import-template="handleImportTemplate"
          @import-config="handleImportConfig"
          @batch-export="handleBatchExport"
          @batch-delete="handleBatchDelete"
          @reset-all="handleResetAll"
        />
        <Button @click="showMaterialDialog = true">
          <Plus class="mr-2 h-4 w-4" />
          新增物料
        </Button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"
      ></div>
    </div>

    <!-- 错误状态 -->
    <Alert v-else-if="error" variant="destructive">
      <AlertDescription> 加载数据时出错：{{ error }} </AlertDescription>
    </Alert>

    <!-- 主内容区 -->
    <div v-else class="grid gap-4 grid-cols-1 xl:grid-cols-7">
      <div class="xl:col-span-2 space-y-4">
        <MaterialCategoryList
          :category-tree="categoryTree"
          :selected-category-id="selectedCategoryId"
          :materials="materials"
          :selected-category="selectedCategory"
          @category-selected="handleCategorySelected"
          @expand-all="handleExpandAll"
          @collapse-all="handleCollapseAll"
        />
      </div>

      <!-- 右侧主内容区 -->
      <div class="xl:col-span-5 space-y-4">
        <!-- 默认状态：未选择分类 -->
        <Card
          v-if="!selectedCategoryId"
          class="hover:shadow-md transition-shadow"
        >
          <CardContent class="p-8 text-center">
            <Package class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 class="text-lg font-medium mb-2">选择物料分类</h3>
            <p class="text-muted-foreground">
              请从左侧选择一个物料分类来查看相关的物料信息
            </p>
          </CardContent>
        </Card>

        <!-- 选择了分类：显示物料列表 -->
        <template v-else>
          <!-- 搜索和过滤器 -->
          <MaterialSearchFilter
            :total-results="materialsForSelectedCategory.length"
            :loading="loading"
            @search="handleSearch"
            @export-results="handleExportSearchResults"
            @share-search="handleShareSearch"
          />

          <div class="hover:shadow-md transition-shadow">
            <MaterialTable
              :materials="filteredMaterials"
              :selected-material-id="selectedMaterialId"
              @material-selected="handleMaterialSelected"
              @material-updated="handleMaterialUpdated"
              @material-duplicated="handleMaterialDuplicated"
              @material-status-changed="handleMaterialStatusChanged"
            />
          </div>
        </template>
      </div>
    </div>

    <!-- 操作对话框 -->
    <CategoryOperationDialog
      :open="operationDialogOpen"
      :category="editingCategory"
      :available-parent-categories="availableParentCategories"
      @update:open="operationDialogOpen = $event"
      @submit="handleCategorySubmit"
    />

    <!-- 批量操作对话框 -->
    <CategoryBatchDialog
      :open="batchDialogOpen"
      :operation="currentBatchOperation"
      :categories="materialCategories"
      @update:open="batchDialogOpen = $event"
      @confirm="handleBatchConfirm"
    />

    <!-- 物料操作对话框 -->
    <MaterialOperationDialog
      :open="showMaterialDialog"
      :material="editingMaterial"
      :available-categories="materialCategories"
      @update:open="showMaterialDialog = $event"
      @submit="handleMaterialSubmit"
    />

    <!-- 物料导入对话框 -->
    <MaterialImportDialog
      :open="showImportDialog"
      @update:open="showImportDialog = $event"
      @import-complete="handleImportComplete"
    />

    <!-- 物料导出对话框 -->
    <MaterialExportDialog
      :open="showExportDialog"
      :selected-materials="
        filteredMaterials.map((m) => ({
          materialId: m.materialId,
          displayName: m.displayName,
          categoryId: m.categoryId,
        }))
      "
      @update:open="showExportDialog = $event"
      @export-complete="handleExportComplete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useMetadataStore } from "@/stores/metadata";
import type {
  MaterialCategory,
  BatchOperationData,
  Material,
} from "@/types/material";
import MaterialCategoryList from "@/components/metadata/MaterialCategoryList.vue";
import MaterialTable from "@/components/metadata/MaterialTable.vue";
import CategoryOperationButtons from "@/components/metadata/CategoryOperationButtons.vue";
import CategoryOperationDialog from "@/components/metadata/CategoryOperationDialog.vue";
import CategoryBatchDialog from "@/components/metadata/CategoryBatchDialog.vue";
import MaterialOperationDialog from "@/components/metadata/MaterialOperationDialog.vue";
import MaterialImportDialog from "@/components/metadata/MaterialImportDialog.vue";
import MaterialExportDialog from "@/components/metadata/MaterialExportDialog.vue";
import MaterialSearchFilter from "@/components/metadata/MaterialSearchFilter.vue";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Package, Plus, Upload, Download } from "lucide-vue-next";

const metadataStore = useMetadataStore();

const {
  materials,
  selectedCategoryId,
  selectedMaterialId,
  loading,
  error,
  materialCategories,
} = storeToRefs(metadataStore);

const {
  expandedCategoryTree: categoryTree,
  materialsForSelectedCategory,
  selectedCategory,
} = storeToRefs(metadataStore);

// 操作对话框状态
const operationDialogOpen = ref(false);
const batchDialogOpen = ref(false);
const showMaterialDialog = ref(false);
const showImportDialog = ref(false);
const showExportDialog = ref(false);
const currentBatchOperation = ref<"export" | "delete" | "import">("export");
const editingCategory = ref<MaterialCategory | null>(null);
const editingMaterial = ref<Material | null>(null);

// 搜索和过滤状态
const searchQuery = ref("");
const searchFilters = ref({
  categoryId: "",
  status: "",
  stockStatus: "",
  priceMin: "",
  priceMax: "",
  dateRange: "",
  supplierId: "",
  attributes: {} as Record<string, string>,
});
const sortBy = ref("name-asc");

// 过滤后的物料列表
const filteredMaterials = computed(() => {
  let result = materialsForSelectedCategory.value;

  // 应用搜索查询
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      (material) =>
        material.materialId.toLowerCase().includes(query) ||
        material.displayName.toLowerCase().includes(query)
    );
  }

  // 应用排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case "name-asc":
        return a.displayName.localeCompare(b.displayName);
      case "name-desc":
        return b.displayName.localeCompare(a.displayName);
      default:
        return 0;
    }
  });

  return result;
});

// 可选的父级分类（排除自身和子级）
const availableParentCategories = computed(() => {
  const currentId = editingCategory.value?.categoryId;
  return materialCategories.value.filter(
    (category) => category.categoryId !== currentId && category.level < 2 // 最多支持3级分类
  );
});

onMounted(async () => {
  await metadataStore.fetchMetadata();
});

const handleCategorySelected = (categoryId: string | null) => {
  if (categoryId) {
    metadataStore.selectCategory(categoryId);
  }
};

const handleMaterialSelected = (materialId: string | null) => {
  metadataStore.selectMaterial(materialId);
};

const handleMaterialUpdated = (material: Material) => {
  console.log("物料更新:", material);
  // TODO: 实现物料更新逻辑
};

const handleMaterialDuplicated = (material: Partial<Material>) => {
  console.log("物料复制:", material);
  // TODO: 实现物料复制逻辑
};

const handleMaterialStatusChanged = (materialId: string) => {
  console.log("物料状态切换:", materialId);
  // TODO: 实现物料状态切换逻辑
};

const handleExpandAll = () => {
  metadataStore.expandAllCategories();
};

const handleCollapseAll = () => {
  metadataStore.collapseAllCategories();
};

// 搜索和过滤处理函数
const handleSearch = (params: {
  query: string;
  filters: {
    categoryId: string;
    status: string;
    stockStatus: string;
    priceMin: string;
    priceMax: string;
    dateRange: string;
    supplierId: string;
    attributes: Record<string, string>;
  };
  sortBy: string;
}) => {
  searchQuery.value = params.query;
  searchFilters.value = params.filters;
  sortBy.value = params.sortBy;
};

const handleExportSearchResults = () => {
  showExportDialog.value = true;
};

const handleShareSearch = (url: string) => {
  // 复制搜索URL到剪贴板
  navigator.clipboard
    .writeText(url)
    .then(() => {
      console.log("搜索链接已复制到剪贴板");
    })
    .catch(() => {
      console.log("复制失败，搜索链接：", url);
    });
};

// 分类操作处理函数
const handleAddCategory = () => {
  editingCategory.value = null;
  operationDialogOpen.value = true;
};

const handleEditCategory = (category: MaterialCategory) => {
  editingCategory.value = category;
  operationDialogOpen.value = true;
};

const handleAddChildCategory = (parentCategory: MaterialCategory) => {
  editingCategory.value = {
    categoryId: "",
    categoryName: "",
    description: "",
    parentId: parentCategory.categoryId,
    level: parentCategory.level + 1,
    hasChildren: false,
    attributeSchema: null,
  } as MaterialCategory;
  operationDialogOpen.value = true;
};

const handleDuplicateCategory = (category: MaterialCategory) => {
  editingCategory.value = {
    ...category,
    categoryId: `${category.categoryId}_COPY_${Date.now()}`,
    categoryName: `${category.categoryName} (副本)`,
    hasChildren: false,
  };
  operationDialogOpen.value = true;
};

const handleDeleteCategory = async (categoryId: string) => {
  try {
    const result = await metadataStore.deleteCategory(categoryId);
    if (result.success) {
      console.log("删除成功:", result.message);
    } else {
      console.error("删除失败:", result.errors);
    }
  } catch (error) {
    console.error("删除时出错:", error);
  }
};

const handleExportCategory = (category: MaterialCategory) => {
  // 导出单个分类配置
  const configData = {
    category,
    exportTime: new Date().toISOString(),
    version: "1.0",
  };

  const blob = new Blob([JSON.stringify(configData, null, 2)], {
    type: "application/json",
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `category_${category.categoryId}.json`;
  a.click();
  URL.revokeObjectURL(url);

  console.log(`导出成功: 分类"${category.categoryName}"已导出到文件`);
};

const handleImportTemplate = () => {
  currentBatchOperation.value = "import";
  batchDialogOpen.value = true;
};

const handleImportConfig = () => {
  currentBatchOperation.value = "import";
  batchDialogOpen.value = true;
};

const handleBatchExport = () => {
  currentBatchOperation.value = "export";
  batchDialogOpen.value = true;
};

const handleBatchDelete = () => {
  currentBatchOperation.value = "delete";
  batchDialogOpen.value = true;
};

const handleResetAll = async () => {
  try {
    const result = await metadataStore.resetAllCategories();
    if (result.success) {
      console.log("重置成功:", result.message);
    } else {
      console.error("重置失败:", result.errors);
    }
  } catch (error) {
    console.error("重置时出错:", error);
  }
};

// 物料操作处理
 
const handleMaterialSubmit = async (materialData: any) => {
  try {
    console.log("提交物料数据:", materialData);
    // TODO: 实现物料创建/更新逻辑
    showMaterialDialog.value = false;
    editingMaterial.value = null;
  } catch (error) {
    console.error("物料操作失败:", error);
  }
};

// 对话框提交处理
const handleCategorySubmit = async (category: MaterialCategory) => {
  try {
    const isEdit = !!editingCategory.value?.categoryId;

    if (isEdit) {
      // 编辑分类
      const result = await metadataStore.updateCategory(category);
      if (result.success) {
        console.log("更新成功:", result.message);
      } else {
        console.error("更新失败:", result.errors);
        return;
      }
    } else {
      // 新增分类
      const result = await metadataStore.addCategory(category);
      if (result.success) {
        console.log("创建成功:", result.message);
      } else {
        console.error("创建失败:", result.errors);
        return;
      }
    }

    operationDialogOpen.value = false;
    editingCategory.value = null;
  } catch (error) {
    console.error("操作时出错:", error);
  }
};

// 批量操作处理
const handleBatchConfirm = async (data: BatchOperationData) => {
  try {
    if (data.exportData) {
      // 批量导出
      await handleBatchExportData(data.exportData);
    } else if (data.deleteData) {
      // 批量删除
      await handleBatchDeleteData(data.deleteData);
    } else if (data.importData) {
      // 批量导入
      await handleBatchImportData(data.importData);
    }
  } catch (error) {
    console.error("批量操作失败:", error);
  }
};

const handleBatchExportData = async (exportData: {
  categoryIds: string[];
  format: string;
}) => {
  const categories = materialCategories.value.filter((c) =>
    exportData.categoryIds.includes(c.categoryId)
  );

  const exportPayload = {
    categories,
    exportTime: new Date().toISOString(),
    version: "1.0",
    format: exportData.format,
  };

  const blob = new Blob([JSON.stringify(exportPayload, null, 2)], {
    type: "application/json",
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `categories_batch_export_${Date.now()}.json`;
  a.click();
  URL.revokeObjectURL(url);

  console.log(`批量导出成功: 已导出 ${categories.length} 个分类配置`);
};

const handleBatchDeleteData = async (deleteData: { categoryIds: string[] }) => {
  // 这里应该调用实际的批量删除API
  console.log(`批量删除成功: 已删除 ${deleteData.categoryIds.length} 个分类`);
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleBatchImportData = async (_importData: any) => {
  // 处理批量导入逻辑
  console.log("批量导入成功: 分类配置已成功导入");
};

// 处理导入导出
const handleImportComplete = (data: unknown[]) => {
  console.log("导入完成:", data);
  showImportDialog.value = false;
  // 重新加载数据
  metadataStore.fetchMetadata();
};

const handleExportComplete = (filename: string) => {
  console.log("导出完成:", filename);
  showExportDialog.value = false;
};
</script>
