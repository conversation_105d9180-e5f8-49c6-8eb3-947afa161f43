<template>
  <div class="container mx-auto p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">测试新建工单界面</h1>
      <div class="flex items-center gap-3">
        <Button @click="showWizard = true" variant="outline">
          <Wand2 class="w-4 h-4 mr-2" />
          工单向导
        </Button>
        <Button @click="showDialog = true" class="bg-blue-600 hover:bg-blue-700">
          <Plus class="w-4 h-4 mr-2" />
          新建工单
        </Button>
        <Button @click="showManagement = true" variant="outline">
          <Settings class="w-4 h-4 mr-2" />
          工单管理
        </Button>
      </div>
    </div>
    
    <div v-if="createdOrders.length > 0" class="mb-6">
      <h2 class="text-lg font-semibold mb-3">已创建的工单</h2>
      <div class="space-y-2">
        <div 
          v-for="orderId in createdOrders" 
          :key="orderId"
          class="p-3 bg-green-50 border border-green-200 rounded-lg"
        >
          <div class="flex items-center gap-2">
            <CheckCircle class="w-4 h-4 text-green-600" />
            <span class="font-medium">工单创建成功: {{ orderId }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 功能展示区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- 快速操作面板 -->
      <div class="lg:col-span-1">
        <QuickActionPanel
          @create-order="showDialog = true"
          @batch-import="handleBatchImport"
          @use-template="handleUseTemplate"
          @quick-copy="handleQuickCopy"
          @repeat-action="handleRepeatAction"
        />
      </div>
      
      <!-- 功能介绍 -->
      <div class="lg:col-span-2">
        <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 class="font-medium text-green-800 mb-2">🚀 增强功能测试指南</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-700">
            <div>
              <h4 class="font-medium mb-2">核心功能</h4>
              <ul class="space-y-1">
                <li>• <strong>两步式工作流</strong>：订单选择 → 批次优化</li>
                <li>• <strong>智能批次优化</strong>：自动分组，提高效率</li>
                <li>• <strong>工艺兼容性检查</strong>：避免生产冲突</li>
                <li>• <strong>成本预估</strong>：实时计算生产成本</li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium mb-2">增强功能</h4>
              <ul class="space-y-1">
                <li>• <strong>工单向导</strong>：引导式创建流程</li>
                <li>• <strong>快速操作</strong>：批量导入、模板复用</li>
                <li>• <strong>工单管理</strong>：完整的生命周期管理</li>
                <li>• <strong>工艺流程配置</strong>：动态配置生产工艺</li>
                <li>• <strong>智能批次优化</strong>：自动分析最优生产方案</li>
                <li>• <strong>状态指示器</strong>：清晰的订单项状态展示</li>
              </ul>
            </div>
          </div>
          
          <div class="mt-4 p-3 bg-white rounded border">
            <div class="text-xs text-gray-600">
              <strong>🎯 测试建议：</strong>
              <br>1. 点击"新建工单"体验两步式工作流
              <br>2. 点击"工单向导"体验引导式创建
              <br>3. 点击"工单管理"查看完整管理界面
              <br>4. 使用快速操作面板测试便捷功能
              <br>5. 尝试选择未定义工艺流程的订单项，体验工艺配置功能
              <br>6. 观察批次优化的效果和建议
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 对话框组件 -->
    <ProductionOrderCreationDialog
      :open="showDialog"
      @update:open="showDialog = $event"
      @order-created="handleOrderCreated"
    />
    
    <!-- 工单向导对话框 -->
    <Dialog :open="showWizard" @update:open="showWizard = $event">
      <DialogContent class="max-w-6xl h-[90vh]">
        <DialogHeader>
          <DialogTitle>工单创建向导</DialogTitle>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <ProductionOrderWizard
            :available-orders="mockOrders"
            :selected-order-items="selectedItems"
            :batch-optimization="batchOptimization"
            :process-conflicts="[]"
            :validation-results="[]"
            :work-order-priority="'normal'"
            :planned-start-date="new Date().toISOString().split('T')[0]"
            :estimated-end-date="''"
            :schedule-recommendation="'建议在工作日进行生产，避开节假日'"
            :search-query="''"
            :status-filter="'all'"
            :process-type-filter="'all'"
            :customer-filter="'all'"
            :loading="false"
            :is-creating="false"
            :can-create-order="selectedItems.length > 0"
            @order-item-selected="handleWizardOrderItemSelected"
            @order-item-removed="handleWizardOrderItemRemoved"
            @quantity-changed="handleWizardQuantityChanged"
            @create-order="handleWizardCreateOrder"
            @cancel="showWizard = false"
          />
        </div>
      </DialogContent>
    </Dialog>
    
    <!-- 工单管理对话框 -->
    <Dialog :open="showManagement" @update:open="showManagement = $event">
      <DialogContent class="max-w-7xl h-[90vh]">
        <DialogHeader>
          <DialogTitle>生产工单管理</DialogTitle>
        </DialogHeader>
        <div class="flex-1 overflow-hidden">
          <ProductionOrderManagementEnhanced />
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { CheckCircle, Plus, Settings, Wand2 } from 'lucide-vue-next'

import ProductionOrderCreationDialog from '@/components/mes/ProductionOrderCreationDialog.vue'
import ProductionOrderWizard from '@/components/mes/ProductionOrderWizard.vue'
import ProductionOrderManagementEnhanced from '@/components/mes/ProductionOrderManagementEnhanced.vue'
import QuickActionPanel from '@/components/mes/QuickActionPanel.vue'

import type { SelectedOrderItem } from '@/types/production-order-creation'

const showDialog = ref(false)
const showWizard = ref(false)
const showManagement = ref(false)
const createdOrders = ref<string[]>([])

// 向导相关状态
const selectedItems = ref<SelectedOrderItem[]>([])
const batchOptimization = ref(null)

// 模拟数据
const mockOrders = ref([
  {
    id: '1',
    orderNumber: 'CO-2024001',
    customerName: '华润置地',
    status: 'confirmed',
    deliveryDate: '2024-02-15',
    items: [
      {
        id: '1-1',
        customerOrderId: '1',
        specifications: {
          length: 1800,
          width: 1200,
          thickness: 6,
          glassType: 'clear',
          color: '透明'
        },
        quantity: 450,
        deliveryDate: '2024-02-15',
        processFlow: [
          {
            stepName: '切割',
            workstation: 'cutting_station_1',
            estimatedDuration: 15,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '磨边',
            workstation: 'edging_station_1',
            estimatedDuration: 20,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '钢化',
            workstation: 'tempering_furnace_1',
            estimatedDuration: 45,
            constraints: ['temperature_control'],
            status: 'pending'
          },
          {
            stepName: '质检',
            workstation: 'quality_station',
            estimatedDuration: 10,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '包装',
            workstation: 'packaging_station',
            estimatedDuration: 5,
            constraints: [],
            status: 'pending'
          }
        ]
      }
    ]
  },
  {
    id: '2',
    orderNumber: 'CO-2024002',
    customerName: '万科集团',
    status: 'confirmed',
    deliveryDate: '2024-02-20',
    items: [
      {
        id: '2-1',
        customerOrderId: '2',
        specifications: {
          length: 2400,
          width: 1600,
          thickness: 8,
          glassType: 'low_e',
          color: '透明'
        },
        quantity: 280,
        deliveryDate: '2024-02-20',
        processFlow: [
          {
            stepName: '切割',
            workstation: 'cutting_station_1',
            estimatedDuration: 18,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '磨边',
            workstation: 'edging_station_1',
            estimatedDuration: 25,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '镀膜',
            workstation: 'coating_line',
            estimatedDuration: 40,
            constraints: ['clean_environment'],
            status: 'pending'
          },
          {
            stepName: '钢化',
            workstation: 'tempering_furnace_1',
            estimatedDuration: 50,
            constraints: ['temperature_control'],
            status: 'pending'
          },
          {
            stepName: '质检',
            workstation: 'quality_station',
            estimatedDuration: 15,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '包装',
            workstation: 'packaging_station',
            estimatedDuration: 8,
            constraints: [],
            status: 'pending'
          }
        ]
      }
    ]
  },
  {
    id: '3',
    orderNumber: 'CO-2024003',
    customerName: '碧桂园',
    status: 'confirmed',
    deliveryDate: '2024-02-18',
    items: [
      {
        id: '3-1',
        customerOrderId: '3',
        specifications: {
          length: 1500,
          width: 1000,
          thickness: 6,
          glassType: 'clear',
          color: '透明'
        },
        quantity: 320,
        deliveryDate: '2024-02-18',
        processFlow: [
          {
            stepName: '切割',
            workstation: 'cutting_station_2',
            estimatedDuration: 12,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '磨边',
            workstation: 'edging_station_2',
            estimatedDuration: 18,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '质检',
            workstation: 'quality_station',
            estimatedDuration: 8,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '包装',
            workstation: 'packaging_station',
            estimatedDuration: 5,
            constraints: [],
            status: 'pending'
          }
        ]
      }
    ]
  },
  {
    id: '4',
    orderNumber: 'CO-2024004',
    customerName: '保利地产',
    status: 'confirmed',
    deliveryDate: '2024-02-25',
    items: [
      {
        id: '4-1',
        customerOrderId: '4',
        specifications: {
          length: 1200,
          width: 800,
          thickness: 5,
          glassType: 'clear',
          color: '透明'
        },
        quantity: 200,
        deliveryDate: '2024-02-25',
        processFlow: [
          {
            stepName: '切割',
            workstation: 'cutting_station_1',
            estimatedDuration: 10,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '磨边',
            workstation: 'edging_station_1',
            estimatedDuration: 15,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '质检',
            workstation: 'quality_station',
            estimatedDuration: 6,
            constraints: [],
            status: 'pending'
          },
          {
            stepName: '包装',
            workstation: 'packaging_station',
            estimatedDuration: 4,
            constraints: [],
            status: 'pending'
          }
        ]
      },
      {
        id: '4-2',
        customerOrderId: '4',
        specifications: {
          length: 1000,
          width: 600,
          thickness: 4,
          glassType: 'clear',
          color: '透明'
        },
        quantity: 150,
        deliveryDate: '2024-02-25',
        processFlow: [] // 这个订单项没有工艺流程，用于测试工艺配置功能
      }
    ]
  }
])

const handleOrderCreated = (orderIds: string[]) => {
  createdOrders.value.push(...orderIds)
  console.log('工单创建成功:', orderIds)
}

// 向导事件处理
const handleWizardOrderItemSelected = (item: any, quantity: number) => {
  console.log('向导选择订单项:', item, quantity)
}

const handleWizardOrderItemRemoved = (itemId: string) => {
  console.log('向导移除订单项:', itemId)
}

const handleWizardQuantityChanged = (itemId: string, quantity: number) => {
  console.log('向导数量变更:', itemId, quantity)
}

const handleWizardCreateOrder = () => {
  console.log('向导创建工单')
  showWizard.value = false
}

// 快速操作事件处理
const handleBatchImport = () => {
  console.log('批量导入')
}

const handleUseTemplate = () => {
  console.log('使用模板')
}

const handleQuickCopy = () => {
  
}

const handleRepeatAction = (action: any) => {
  
}
</script>