<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">元数据管理</h1>
      <div class="flex items-center space-x-2">
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="outline">
              <Download class="mr-2 h-4 w-4" />
              导出配置
              <ChevronDown class="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>导出选项</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem @click="exportConfig('json')">
              <FileText class="mr-2 h-4 w-4" />
              JSON 格式
            </DropdownMenuItem>
            <DropdownMenuItem @click="exportConfig('excel')">
              <FileSpreadsheet class="mr-2 h-4 w-4" />
              Excel 格式
            </DropdownMenuItem>
            <DropdownMenuItem @click="exportConfig('xml')">
              <Code class="mr-2 h-4 w-4" />
              XML 格式
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button>
              <Plus class="mr-2 h-4 w-4" />
              新增配置
              <ChevronDown class="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>配置类型</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem @click="createNew('material')">
              <Package class="mr-2 h-4 w-4" />
              物料分类
            </DropdownMenuItem>
            <DropdownMenuItem @click="createNew('process')">
              <Settings class="mr-2 h-4 w-4" />
              工艺路线
            </DropdownMenuItem>
            <DropdownMenuItem @click="createNew('user')">
              <Users class="mr-2 h-4 w-4" />
              用户角色
            </DropdownMenuItem>
            <DropdownMenuItem @click="createNew('dict')">
              <Database class="mr-2 h-4 w-4" />
              数据字典
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>

    <Tabs default-value="overview" class="space-y-6">
      <TabsList class="grid w-full grid-cols-4">
        <TabsTrigger value="overview">概览</TabsTrigger>
        <TabsTrigger value="materials">物料管理</TabsTrigger>
        <TabsTrigger value="system">系统配置</TabsTrigger>
        <TabsTrigger value="activities">活动日志</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview" class="space-y-6">
        <!-- 快速导航卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card class="cursor-pointer hover:shadow-md transition-shadow" @click="navigateToMaterialVariants">
            <CardHeader>
              <CardTitle class="flex items-center">
                <Package class="h-5 w-5 mr-2" />
                物料变体管理
              </CardTitle>
              <CardDescription>
                配置物料分类、模板和变体规格
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-blue-600">{{ materialVariantCount }}</div>
              <p class="text-sm text-muted-foreground">已配置的物料变体</p>
            </CardContent>
          </Card>

          <Card class="cursor-pointer hover:shadow-md transition-shadow" @click="showProcessDialog = true">
            <CardHeader>
              <CardTitle class="flex items-center">
                <Settings class="h-5 w-5 mr-2" />
                工艺路线配置
              </CardTitle>
              <CardDescription>
                定义生产工艺流程和参数
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-green-600">{{ processRouteCount }}</div>
              <p class="text-sm text-muted-foreground">工艺路线模板</p>
            </CardContent>
          </Card>

          <Card class="cursor-pointer hover:shadow-md transition-shadow" @click="showUserDialog = true">
            <CardHeader>
              <CardTitle class="flex items-center">
                <Users class="h-5 w-5 mr-2" />
                用户角色管理
              </CardTitle>
              <CardDescription>
                配置用户权限和角色分配
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-purple-600">{{ userRoleCount }}</div>
              <p class="text-sm text-muted-foreground">用户角色类型</p>
            </CardContent>
          </Card>

          <Card class="cursor-pointer hover:shadow-md transition-shadow" @click="showDictDialog = true">
            <CardHeader>
              <CardTitle class="flex items-center">
                <Database class="h-5 w-5 mr-2" />
                数据字典
              </CardTitle>
              <CardDescription>
                管理系统基础数据和枚举值
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-orange-600">{{ dataDictionaryCount }}</div>
              <p class="text-sm text-muted-foreground">字典项目</p>
            </CardContent>
          </Card>

          <Card class="cursor-pointer hover:shadow-md transition-shadow" @click="showParamDialog = true">
            <CardHeader>
              <CardTitle class="flex items-center">
                <Cog class="h-5 w-5 mr-2" />
                系统参数
              </CardTitle>
              <CardDescription>
                配置系统运行参数和业务规则
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-red-600">{{ systemParamCount }}</div>
              <p class="text-sm text-muted-foreground">系统参数</p>
            </CardContent>
          </Card>

          <Card class="cursor-pointer hover:shadow-md transition-shadow" @click="showReportDialog = true">
            <CardHeader>
              <CardTitle class="flex items-center">
                <FileText class="h-5 w-5 mr-2" />
                报表模板
              </CardTitle>
              <CardDescription>
                设计和管理各类业务报表模板
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-indigo-600">{{ reportTemplateCount }}</div>
              <p class="text-sm text-muted-foreground">报表模板</p>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
      
      <TabsContent value="materials">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span>物料元数据管理</span>
              <Button @click="navigateToMaterialVariants">
                <ExternalLink class="mr-2 h-4 w-4" />
                进入完整管理界面
              </Button>
            </CardTitle>
            <CardDescription>
              管理物料分类、基础属性和库存变体信息
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div class="text-center py-8">
              <Package class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 class="text-lg font-medium mb-2">物料元数据管理</h3>
              <p class="text-muted-foreground mb-4">
                这里提供物料分类、模板配置和变体管理的完整功能
              </p>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600">{{ materialVariantCount }}</div>
                  <div class="text-muted-foreground">物料变体</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">156</div>
                  <div class="text-muted-foreground">分类配置</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600">24</div>
                  <div class="text-muted-foreground">模板配置</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="system" class="space-y-6">
        <Alert>
          <AlertCircle class="h-4 w-4" />
          <AlertTitle>系统配置</AlertTitle>
          <AlertDescription>
            系统级配置需要管理员权限，请确保您有足够的权限进行操作。
          </AlertDescription>
        </Alert>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>系统参数配置</CardTitle>
              <CardDescription>配置系统运行的核心参数</CardDescription>
            </CardHeader>
            <CardContent>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium">自动备份</span>
                  <Badge variant="default">已启用</Badge>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium">数据同步</span>
                  <Badge variant="secondary">每小时</Badge>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium">日志保留</span>
                  <Badge variant="outline">30天</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>数据字典管理</CardTitle>
              <CardDescription>管理系统枚举值和基础数据</CardDescription>
            </CardHeader>
            <CardContent>
              <div class="space-y-2">
                <div class="flex items-center justify-between py-2">
                  <span class="text-sm">玻璃类型</span>
                  <span class="text-xs text-muted-foreground">12 项</span>
                </div>
                <div class="flex items-center justify-between py-2">
                  <span class="text-sm">工艺类型</span>
                  <span class="text-xs text-muted-foreground">8 项</span>
                </div>
                <div class="flex items-center justify-between py-2">
                  <span class="text-sm">质量等级</span>
                  <span class="text-xs text-muted-foreground">5 项</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
      
      <TabsContent value="activities">
        <!-- 最近配置活动 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span>最近配置活动</span>
              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <Button variant="outline" size="sm">
                    <Filter class="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>活动类型</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuCheckboxItem checked>
                    新增
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem checked>
                    更新
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem checked>
                    删除
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardTitle>
            <CardDescription>
              查看最近的元数据配置变更记录
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div v-for="activity in recentActivities" :key="activity.id"
                   class="flex items-start p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                <div class="flex-1 space-y-1">
                  <p class="text-sm font-medium leading-none">
                    {{ activity.title }}
                  </p>
                  <p class="text-xs text-muted-foreground">
                    {{ activity.description }}
                  </p>
                  <p class="text-xs text-muted-foreground">
                    {{ activity.time }} · {{ activity.user }}
                  </p>
                </div>
                <Badge :variant="activity.type === 'create' ? 'default' : activity.type === 'update' ? 'secondary' : 'destructive'">
                  {{ activity.type === 'create' ? '新增' : activity.type === 'update' ? '更新' : '删除' }}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>

    <!-- 快速访问对话框 -->
    <Dialog :open="showProcessDialog" @update:open="showProcessDialog = $event">
      <DialogContent class="sm:max-w-[500px] !max-h-[90vh]">
        <DialogHeader>
          <DialogTitle class="flex items-center">
            <Settings class="mr-2 h-5 w-5" />
            工艺路线配置
          </DialogTitle>
          <DialogDescription>
            管理和配置生产工艺流程模板
          </DialogDescription>
        </DialogHeader>
        <div class="py-4">
          <p class="text-sm text-muted-foreground mb-4">
            当前系统中共有 {{ processRouteCount }} 个工艺路线模板，包括切割、钢化、夹胶等主要工艺流程。
          </p>
          <div class="space-y-2">
            <div class="flex items-center justify-between p-2 bg-muted/30 rounded">
              <span class="text-sm">玻璃切割工艺</span>
              <Badge variant="secondary">标准</Badge>
            </div>
            <div class="flex items-center justify-between p-2 bg-muted/30 rounded">
              <span class="text-sm">钢化处理工艺</span>
              <Badge variant="secondary">标准</Badge>
            </div>
            <div class="flex items-center justify-between p-2 bg-muted/30 rounded">
              <span class="text-sm">夹胶复合工艺</span>
              <Badge variant="outline">自定义</Badge>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="closeProcessDialog">关闭</Button>
          <Button @click="navigateToProcess">详细管理</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 其他对话框类似结构... -->
    <Dialog :open="showUserDialog" @update:open="showUserDialog = $event">
      <DialogContent class="sm:max-w-[500px] !max-h-[90vh]">
        <DialogHeader>
          <DialogTitle class="flex items-center">
            <Users class="mr-2 h-5 w-5" />
            用户角色管理
          </DialogTitle>
          <DialogDescription>
            配置用户权限和角色分配
          </DialogDescription>
        </DialogHeader>
        <div class="py-4">
          <p class="text-sm text-muted-foreground">
            用户角色管理功能即将上线，敬请期待。
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="closeUserDialog">关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 占位对话框 -->
    <Dialog :open="showDictDialog" @update:open="showDictDialog = $event">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>数据字典</DialogTitle>
          <DialogDescription>系统基础数据管理</DialogDescription>
        </DialogHeader>
        <div class="py-4">
          <p class="text-sm text-muted-foreground">数据字典功能开发中...</p>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="closeDictDialog">关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <Dialog :open="showParamDialog" @update:open="showParamDialog = $event">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>系统参数</DialogTitle>
          <DialogDescription>系统运行参数配置</DialogDescription>
        </DialogHeader>
        <div class="py-4">
          <p class="text-sm text-muted-foreground">系统参数功能开发中...</p>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="closeParamDialog">关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <Dialog :open="showReportDialog" @update:open="showReportDialog = $event">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>报表模板</DialogTitle>
          <DialogDescription>业务报表模板管理</DialogDescription>
        </DialogHeader>
        <div class="py-4">
          <p class="text-sm text-muted-foreground">报表模板功能开发中...</p>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="closeReportDialog">关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuCheckboxItem } from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  Plus, 
  Download, 
  Package, 
  Settings, 
  Users, 
  Database, 
  Cog, 
  FileText,
  ChevronDown,
  FileSpreadsheet,
  Code,
  Filter,
  AlertCircle,
  ExternalLink
} from 'lucide-vue-next'

const router = useRouter()

// 对话框状态
const showProcessDialog = ref(false)
const showUserDialog = ref(false)
const showDictDialog = ref(false)
const showParamDialog = ref(false)
const showReportDialog = ref(false)

// 模拟数据统计
const materialVariantCount = ref(156)
const processRouteCount = ref(24)
const userRoleCount = ref(8)
const dataDictionaryCount = ref(89)
const systemParamCount = ref(45)
const reportTemplateCount = ref(12)

// 最近活动数据
const recentActivities = computed(() => [
  {
    id: 1,
    title: '新增玻璃物料变体模板',
    description: '添加了12mm钢化玻璃的物料变体配置',
    time: '2小时前',
    user: '张工程师',
    type: 'create'
  },
  {
    id: 2,
    title: '更新切割工艺参数',
    description: '优化了玻璃切割工艺的默认参数设置',
    time: '4小时前',
    user: '李主管',
    type: 'update'
  },
  {
    id: 3,
    title: '新增用户角色',
    description: '创建了"质量检验员"角色并配置相关权限',
    time: '1天前',
    user: '王管理员',
    type: 'create'
  },
  {
    id: 4,
    title: '更新数据字典',
    description: '更新了玻璃颜色分类的枚举值',
    time: '2天前',
    user: '陈工程师',
    type: 'update'
  },
  {
    id: 5,
    title: '删除过期模板',
    description: '清理了不再使用的旧版工艺路线模板',
    time: '3天前',
    user: '系统管理员',
    type: 'delete'
  }
])

// 导航方法
const navigateToMaterialVariants = () => {
  router.push('/metadata/materials')
}

const navigateToProcess = () => {
  closeProcessDialog()
  // TODO: 实现工艺路线页面导航
  console.log('导航到工艺路线管理')
}

// 对话框关闭方法
const closeProcessDialog = () => {
  showProcessDialog.value = false
}

const closeUserDialog = () => {
  showUserDialog.value = false
}

const closeDictDialog = () => {
  showDictDialog.value = false
}

const closeParamDialog = () => {
  showParamDialog.value = false
}

const closeReportDialog = () => {
  showReportDialog.value = false
}

// 导出配置方法
const exportConfig = (format: string) => {
  console.log(`导出 ${format} 格式配置`)
  // TODO: 实现具体的导出功能
}

// 创建新配置方法
const createNew = (type: string) => {
  console.log(`创建新的 ${type} 配置`)
  // TODO: 实现具体的创建功能
  switch (type) {
    case 'material':
      router.push('/metadata/materials?action=create')
      break
    case 'process':
      showProcessDialog.value = true
      break
    case 'user':
      showUserDialog.value = true
      break
    case 'dict':
      showDictDialog.value = true
      break
  }
}
</script>