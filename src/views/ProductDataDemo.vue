<template>
  <div class="product-data-demo p-6">
    <h1 class="text-3xl font-bold mb-8">产品管理Mock数据演示</h1>
    
    <!-- 组件数据演示 -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold mb-4">组件数据</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex gap-4 mb-4">
          <button 
            @click="loadComponents"
            :disabled="componentStore.loading"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {{ componentStore.loading ? '加载中...' : '加载组件' }}
          </button>
          <select 
            v-model="componentTypeFilter"
            @change="filterComponents"
            class="px-3 py-2 border rounded"
          >
            <option value="">所有类型</option>
            <option value="frame">框架</option>
            <option value="glass">玻璃</option>
            <option value="hardware">五金</option>
            <option value="seal">密封</option>
          </select>
        </div>
        
        <div v-if="componentStore.error" class="text-red-500 mb-4">
          错误: {{ componentStore.error }}
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div 
            v-for="component in componentStore.filteredComponents" 
            :key="component.id"
            class="border rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <h3 class="font-semibold text-lg">{{ component.name }}</h3>
            <p class="text-gray-600 text-sm">{{ component.code }}</p>
            <p class="text-gray-500 text-xs mt-2">{{ component.description }}</p>
            <div class="mt-3 flex justify-between items-center">
              <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                {{ component.componentType }}
              </span>
              <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                {{ component.status }}
              </span>
            </div>
            <div class="mt-2 text-sm text-gray-600">
              参数: {{ component.parameters?.length || 0 }} 个
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 构件数据演示 -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold mb-4">构件数据</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <button 
          @click="loadAssemblies"
          :disabled="assemblyStore.loading"
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 mb-4"
        >
          {{ assemblyStore.loading ? '加载中...' : '加载构件' }}
        </button>
        
        <div v-if="assemblyStore.error" class="text-red-500 mb-4">
          错误: {{ assemblyStore.error }}
        </div>
        
        <div class="space-y-4">
          <div 
            v-for="assembly in assemblyStore.assemblies" 
            :key="assembly.id"
            class="border rounded-lg p-4"
          >
            <h3 class="font-semibold text-lg">{{ assembly.name }}</h3>
            <p class="text-gray-600 text-sm">{{ assembly.code }}</p>
            <p class="text-gray-500 text-xs mt-2">{{ assembly.description }}</p>
            <div class="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span class="font-medium">类型:</span>
                <span class="ml-1">{{ assembly.assemblyType }}</span>
              </div>
              <div>
                <span class="font-medium">组件实例:</span>
                <span class="ml-1">{{ assembly.componentInstances?.length || 0 }}</span>
              </div>
              <div>
                <span class="font-medium">子构件:</span>
                <span class="ml-1">{{ assembly.subAssemblies?.length || 0 }}</span>
              </div>
              <div>
                <span class="font-medium">状态:</span>
                <span class="ml-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                  {{ assembly.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品结构数据演示 -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold mb-4">产品结构数据</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <button 
          @click="loadProductStructures"
          :disabled="structureStore.loading"
          class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 mb-4"
        >
          {{ structureStore.loading ? '加载中...' : '加载产品结构' }}
        </button>
        
        <div v-if="structureStore.error" class="text-red-500 mb-4">
          错误: {{ structureStore.error }}
        </div>
        
        <div class="space-y-4">
          <div 
            v-for="structure in structureStore.structures" 
            :key="structure.id"
            class="border rounded-lg p-4"
          >
            <h3 class="font-semibold text-lg">{{ structure.name }}</h3>
            <p class="text-gray-600 text-sm">{{ structure.code }}</p>
            <p class="text-gray-500 text-xs mt-2">{{ structure.description }}</p>
            <div class="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span class="font-medium">产品类型:</span>
                <span class="ml-1">{{ structure.productType }}</span>
              </div>
              <div>
                <span class="font-medium">类别:</span>
                <span class="ml-1">{{ structure.category }}</span>
              </div>
              <div>
                <span class="font-medium">参数:</span>
                <span class="ml-1">{{ structure.productParameters?.length || 0 }}</span>
              </div>
              <div>
                <span class="font-medium">约束:</span>
                <span class="ml-1">{{ structure.productConstraints?.length || 0 }}</span>
              </div>
            </div>
            <div class="mt-2 flex flex-wrap gap-1">
              <span 
                v-for="tag in structure.tags" 
                :key="tag"
                class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品数据演示 -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold mb-4">产品数据</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <button 
          @click="loadProducts"
          :disabled="productStore.loading"
          class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 mb-4"
        >
          {{ productStore.loading ? '加载中...' : '加载产品' }}
        </button>
        
        <div v-if="productStore.error" class="text-red-500 mb-4">
          错误: {{ productStore.error }}
        </div>
        
        <div class="space-y-4">
          <div 
            v-for="product in productStore.products" 
            :key="product.id"
            class="border rounded-lg p-4"
          >
            <h3 class="font-semibold text-lg">{{ product.name }}</h3>
            <p class="text-gray-600 text-sm">{{ product.code }}</p>
            <p class="text-gray-500 text-xs mt-2">{{ product.description }}</p>
            <div class="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span class="font-medium">类别:</span>
                <span class="ml-1">{{ product.category }}</span>
              </div>
              <div>
                <span class="font-medium">生命周期:</span>
                <span class="ml-1">{{ product.lifecycle }}</span>
              </div>
              <div>
                <span class="font-medium">报价BOM:</span>
                <span class="ml-1">{{ product.statistics?.quoteBOMCount || 0 }}</span>
              </div>
              <div>
                <span class="font-medium">生产BOM:</span>
                <span class="ml-1">{{ product.statistics?.productionBOMCount || 0 }}</span>
              </div>
            </div>
            <div class="mt-2 flex flex-wrap gap-1">
              <span 
                v-for="tag in product.tags" 
                :key="tag"
                class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- BOM数据演示 -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold mb-4">BOM数据</h2>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex gap-4 mb-4">
          <button 
            @click="loadQuoteBOMs"
            :disabled="bomStore.loading"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            {{ bomStore.loading ? '加载中...' : '加载报价BOM' }}
          </button>
          <button 
            @click="loadProductionBOMs"
            :disabled="bomStore.loading"
            class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:opacity-50"
          >
            {{ bomStore.loading ? '加载中...' : '加载生产BOM' }}
          </button>
        </div>
        
        <div v-if="bomStore.error" class="text-red-500 mb-4">
          错误: {{ bomStore.error }}
        </div>
        
        <!-- 报价BOM -->
        <div v-if="bomStore.quoteBOMs.length > 0" class="mb-6">
          <h3 class="text-lg font-semibold mb-3">报价BOM</h3>
          <div class="space-y-3">
            <div 
              v-for="bom in bomStore.quoteBOMs" 
              :key="bom.id"
              class="border rounded-lg p-3"
            >
              <h4 class="font-medium">{{ bom.name }}</h4>
              <p class="text-sm text-gray-600">{{ bom.code }}</p>
              <div class="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span class="font-medium">产品:</span>
                  <span class="ml-1">{{ bom.productName }}</span>
                </div>
                <div>
                  <span class="font-medium">状态:</span>
                  <span class="ml-1">{{ bom.status }}</span>
                </div>
                <div>
                  <span class="font-medium">项目数:</span>
                  <span class="ml-1">{{ bom.items?.length || 0 }}</span>
                </div>
                <div>
                  <span class="font-medium">总成本:</span>
                  <span class="ml-1">¥{{ bom.costSummary?.totalCost?.toFixed(2) || '0.00' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 生产BOM -->
        <div v-if="bomStore.productionBOMs.length > 0">
          <h3 class="text-lg font-semibold mb-3">生产BOM</h3>
          <div class="space-y-3">
            <div 
              v-for="bom in bomStore.productionBOMs" 
              :key="bom.id"
              class="border rounded-lg p-3"
            >
              <h4 class="font-medium">{{ bom.name }}</h4>
              <p class="text-sm text-gray-600">{{ bom.code }}</p>
              <div class="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span class="font-medium">产品:</span>
                  <span class="ml-1">{{ bom.productName }}</span>
                </div>
                <div>
                  <span class="font-medium">状态:</span>
                  <span class="ml-1">{{ bom.status }}</span>
                </div>
                <div>
                  <span class="font-medium">项目数:</span>
                  <span class="ml-1">{{ bom.items?.length || 0 }}</span>
                </div>
                <div>
                  <span class="font-medium">总成本:</span>
                  <span class="ml-1">¥{{ bom.costSummary?.totalCost?.toFixed(2) || '0.00' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  useComponentStore,
  useAssemblyStore,
  useProductStructureStore,
  useProductStore,
  useBOMStore
} from '@/stores/productStore';

// 使用stores
const componentStore = useComponentStore();
const assemblyStore = useAssemblyStore();
const structureStore = useProductStructureStore();
const productStore = useProductStore();
const bomStore = useBOMStore();

// 筛选条件
const componentTypeFilter = ref('');

// 方法
const loadComponents = async () => {
  await componentStore.loadComponents();
};

const filterComponents = async () => {
  await componentStore.loadComponents({
    componentType: componentTypeFilter.value || undefined
  });
};

const loadAssemblies = async () => {
  await assemblyStore.loadAssemblies();
};

const loadProductStructures = async () => {
  await structureStore.loadStructures();
};

const loadProducts = async () => {
  await productStore.loadProducts();
};

const loadQuoteBOMs = async () => {
  await bomStore.loadQuoteBOMs();
};

const loadProductionBOMs = async () => {
  await bomStore.loadProductionBOMs();
};

// 页面加载时自动加载组件数据
onMounted(() => {
  loadComponents();
});
</script>

<style scoped>
.product-data-demo {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
