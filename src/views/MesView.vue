<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">MTO-MES 制造执行系统</h1>
        <p class="text-gray-600 mt-1">玻璃深加工MTO模式生产执行系统原型</p>
      </div>
      <div class="flex gap-3">
        <Button variant="outline" @click="refreshData">
          <RefreshCw class="w-4 h-4 mr-2" />
          刷新数据
        </Button>
        <Button @click="showCreateDialog = true">
          <Plus class="w-4 h-4 mr-2" />
          新建生产任务
        </Button>
      </div>
    </div>

    <!-- 系统概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">活跃订单</p>
              <p class="text-3xl font-bold text-blue-600">{{ systemStats.activeOrders }}</p>
            </div>
            <div class="p-3 bg-blue-100 rounded-full">
              <FileText class="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <p class="text-xs text-gray-500 mt-2">本月新增 {{ systemStats.newOrdersThisMonth }} 个</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">排版任务</p>
              <p class="text-3xl font-bold text-orange-600">{{ systemStats.cuttingTasks }}</p>
            </div>
            <div class="p-3 bg-orange-100 rounded-full">
              <Scissors class="w-6 h-6 text-orange-600" />
            </div>
          </div>
          <p class="text-xs text-gray-500 mt-2">平均利用率 {{ systemStats.avgUtilization }}%</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">生产批次</p>
              <p class="text-3xl font-bold text-purple-600">{{ systemStats.productionBatches }}</p>
            </div>
            <div class="p-3 bg-purple-100 rounded-full">
              <Factory class="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <p class="text-xs text-gray-500 mt-2">进行中 {{ systemStats.activeBatches }} 个</p>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">设备利用率</p>
              <p class="text-3xl font-bold text-green-600">{{ systemStats.equipmentUtilization }}%</p>
            </div>
            <div class="p-3 bg-green-100 rounded-full">
              <TrendingUp class="w-6 h-6 text-green-600" />
            </div>
          </div>
          <p class="text-xs text-gray-500 mt-2">较上月提升 {{ systemStats.utilizationImprovement }}%</p>
        </CardContent>
      </Card>
    </div>

    <!-- 核心功能入口 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 客户订单管理 -->
      <Card class="hover:shadow-lg transition-shadow cursor-pointer" @click="navigateTo('/mes/orders')">
        <CardHeader>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-blue-100 rounded-lg">
              <FileText class="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <CardTitle class="text-lg">客户订单管理</CardTitle>
              <CardDescription>管理客户订单，接收销售需求</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">已确认订单</span>
              <span class="font-medium">{{ orderStats.confirmed }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">生产中订单</span>
              <span class="font-medium">{{ orderStats.inProgress }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">已完成订单</span>
              <span class="font-medium">{{ orderStats.completed }}</span>
            </div>
          </div>
          <div class="mt-4 p-3 bg-blue-50 rounded-lg">
            <p class="text-xs text-blue-800 font-medium">业务起点：</p>
            <p class="text-xs text-blue-600">客户订单 → 生产工单转换</p>
          </div>
        </CardContent>
      </Card>

      <!-- 生产工单管理 -->
      <Card class="hover:shadow-lg transition-shadow cursor-pointer" @click="navigateTo('/mes/production-orders')">
        <CardHeader>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-indigo-100 rounded-lg">
              <ClipboardList class="w-6 h-6 text-indigo-600" />
            </div>
            <div>
              <CardTitle class="text-lg">生产工单管理</CardTitle>
              <CardDescription>管理生产工单，驱动生产执行</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">待发布工单</span>
              <span class="font-medium">{{ productionOrderStats.pending }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">执行中工单</span>
              <span class="font-medium">{{ productionOrderStats.inProgress }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">紧急工单</span>
              <span class="font-medium text-red-600">{{ productionOrderStats.urgent }}</span>
            </div>
          </div>
          <div class="mt-4 p-3 bg-indigo-50 rounded-lg">
            <p class="text-xs text-indigo-800 font-medium">执行驱动：</p>
            <p class="text-xs text-indigo-600">生产工单 → 排版任务 → 生产批次</p>
          </div>
        </CardContent>
      </Card>

      <!-- 排版优化 -->
      <Card class="hover:shadow-lg transition-shadow cursor-pointer" @click="navigateTo('/mes/cutting-optimization')">
        <CardHeader>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-orange-100 rounded-lg">
              <Scissors class="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <CardTitle class="text-lg">排版优化</CardTitle>
              <CardDescription>智能排版算法，提升原片利用率</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">待优化任务</span>
              <span class="font-medium">{{ cuttingStats.pending }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">已完成任务</span>
              <span class="font-medium">{{ cuttingStats.completed }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">平均利用率</span>
              <span class="font-medium text-green-600">{{ cuttingStats.avgUtilization }}%</span>
            </div>
          </div>
          <div class="mt-4 p-3 bg-orange-50 rounded-lg">
            <p class="text-xs text-orange-800 font-medium">价值提升：</p>
            <p class="text-xs text-orange-600">利用率提升 5-8%，年节省成本 ¥12万+</p>
          </div>
        </CardContent>
      </Card>

      <!-- 生产调度 -->
      <Card class="hover:shadow-lg transition-shadow cursor-pointer" @click="navigateTo('/mes/production-scheduling')">
        <CardHeader>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-purple-100 rounded-lg">
              <Factory class="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <CardTitle class="text-lg">生产调度</CardTitle>
              <CardDescription>批次管理，工段协同调度</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">计划批次</span>
              <span class="font-medium">{{ productionStats.planned }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">进行中批次</span>
              <span class="font-medium">{{ productionStats.inProgress }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">设备利用率</span>
              <span class="font-medium text-green-600">{{ productionStats.utilization }}%</span>
            </div>
          </div>
          <div class="mt-4 p-3 bg-purple-50 rounded-lg">
            <p class="text-xs text-purple-800 font-medium">效率提升：</p>
            <p class="text-xs text-purple-600">半成品周转时间减少 50%</p>
          </div>
        </CardContent>
      </Card>

      <!-- 交期管理 -->
      <Card class="hover:shadow-lg transition-shadow cursor-pointer" @click="navigateTo('/mes/delivery-management')">
        <CardHeader>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-green-100 rounded-lg">
              <Calendar class="w-6 h-6 text-green-600" />
            </div>
            <div>
              <CardTitle class="text-lg">交期管理</CardTitle>
              <CardDescription>智能交期预测，进度跟踪</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">准时交付率</span>
              <span class="font-medium text-green-600">{{ deliveryStats.onTimeRate }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">预警订单</span>
              <span class="font-medium text-yellow-600">{{ deliveryStats.warnings }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">客户满意度</span>
              <span class="font-medium">{{ deliveryStats.satisfaction }}/5</span>
            </div>
          </div>
          <div class="mt-4 p-3 bg-green-50 rounded-lg">
            <p class="text-xs text-green-800 font-medium">准确性提升：</p>
            <p class="text-xs text-green-600">交期预测准确率达到 88%</p>
          </div>
        </CardContent>
      </Card>

      <!-- 质量管理 -->
      <Card class="hover:shadow-lg transition-shadow cursor-pointer" @click="navigateTo('/mes/quality-management')">
        <CardHeader>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-red-100 rounded-lg">
              <CheckCircle class="w-6 h-6 text-red-600" />
            </div>
            <div>
              <CardTitle class="text-lg">质量管理</CardTitle>
              <CardDescription>质检流程，追溯管理</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">合格率</span>
              <span class="font-medium text-green-600">{{ qualityStats.passRate }}%</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">待检验</span>
              <span class="font-medium">{{ qualityStats.pending }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">不合格品</span>
              <span class="font-medium text-red-600">{{ qualityStats.defective }}</span>
            </div>
          </div>
          <div class="mt-4 p-3 bg-red-50 rounded-lg">
            <p class="text-xs text-red-800 font-medium">质量追溯：</p>
            <p class="text-xs text-red-600">从原材料到成品的完整追溯链</p>
          </div>
        </CardContent>
      </Card>

      <!-- 工段管理 -->
      <Card class="hover:shadow-lg transition-shadow cursor-pointer" @click="navigateTo('/mes/workstations')">
        <CardHeader>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-indigo-100 rounded-lg">
              <LayoutGrid class="w-6 h-6 text-indigo-600" />
            </div>
            <div>
              <CardTitle class="text-lg">工段管理</CardTitle>
              <CardDescription>工段状态，半成品流转</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">冷加工</span>
              <span class="font-medium text-blue-600">运行中</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">钢化</span>
              <span class="font-medium text-orange-600">运行中</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">合片</span>
              <span class="font-medium text-green-600">运行中</span>
            </div>
          </div>
          <div class="mt-4 p-3 bg-indigo-50 rounded-lg">
            <p class="text-xs text-indigo-800 font-medium">协同效率：</p>
            <p class="text-xs text-indigo-600">工段间流转时间优化 30%</p>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 验证演示入口 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Zap class="w-5 h-5" />
          核心价值验证演示
        </CardTitle>
        <CardDescription>
          通过对比演示验证MTO-MES系统的核心业务价值
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button 
            variant="outline" 
            class="h-auto p-4 flex flex-col items-start gap-2"
            @click="navigateTo('/mes/validation/cutting')"
          >
            <div class="flex items-center gap-2 w-full">
              <PanelRight class="w-4 h-4 text-orange-600" />
              <span class="font-medium">排版优化验证</span>
            </div>
            <p class="text-xs text-gray-600 text-left">验证智能排版能否提升5-8%利用率</p>
          </Button>

          <Button 
            variant="outline" 
            class="h-auto p-4 flex flex-col items-start gap-2"
            @click="navigateTo('/mes/validation/scheduling')"
          >
            <div class="flex items-center gap-2 w-full">
              <PanelsTopLeft class="w-4 h-4 text-purple-600" />
              <span class="font-medium">工段调度验证</span>
            </div>
            <p class="text-xs text-gray-600 text-left">验证半成品精确调度的效率提升</p>
          </Button>

          <Button 
            variant="outline" 
            class="h-auto p-4 flex flex-col items-start gap-2"
            @click="navigateTo('/mes/validation/delivery')"
          >
            <div class="flex items-center gap-2 w-full">
              <CalendarCheck2 class="w-4 h-4 text-green-600" />
              <span class="font-medium">交期承诺验证</span>
            </div>
            <p class="text-xs text-gray-600 text-left">验证智能交期预测的准确性提升</p>
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 数据流转可视化 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <GitBranch class="w-5 h-5" />
          端到端数据流转
        </CardTitle>
        <CardDescription>
          MTO模式下的完整业务数据流转链路
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-lg">
          <div class="flex items-center justify-between">
            <div class="text-center">
              <div class="w-16 h-16 bg-blue-500 text-white rounded-full flex items-center justify-center mb-3">
                <FileText class="w-8 h-8" />
              </div>
              <p class="font-medium text-sm">客户订单</p>
              <p class="text-xs text-gray-600">{{ systemStats.activeOrders }}个活跃</p>
            </div>
            <ArrowRight class="w-6 h-6 text-gray-400" />
            <div class="text-center">
              <div class="w-16 h-16 bg-indigo-500 text-white rounded-full flex items-center justify-center mb-3">
                <ClipboardList class="w-8 h-8" />
              </div>
              <p class="font-medium text-sm">生产工单</p>
              <p class="text-xs text-gray-600">{{ systemStats.productionOrders }}个工单</p>
            </div>
            <ArrowRight class="w-6 h-6 text-gray-400" />
            <div class="text-center">
              <div class="w-16 h-16 bg-orange-500 text-white rounded-full flex items-center justify-center mb-3">
                <Scissors class="w-8 h-8" />
              </div>
              <p class="font-medium text-sm">排版任务</p>
              <p class="text-xs text-gray-600">{{ systemStats.cuttingTasks }}个任务</p>
            </div>
            <ArrowRight class="w-6 h-6 text-gray-400" />
            <div class="text-center">
              <div class="w-16 h-16 bg-purple-500 text-white rounded-full flex items-center justify-center mb-3">
                <Factory class="w-8 h-8" />
              </div>
              <p class="font-medium text-sm">生产批次</p>
              <p class="text-xs text-gray-600">{{ systemStats.productionBatches }}个批次</p>
            </div>
            <ArrowRight class="w-6 h-6 text-gray-400" />
            <div class="text-center">
              <div class="w-16 h-16 bg-green-500 text-white rounded-full flex items-center justify-center mb-3">
                <CheckCircle class="w-8 h-8" />
              </div>
              <p class="font-medium text-sm">质检交付</p>
              <p class="text-xs text-gray-600">{{ qualityStats.passRate }}%合格率</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 创建任务对话框 -->
    <Dialog v-model:open="showCreateDialog">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>新建生产任务</DialogTitle>
        </DialogHeader>
        <div class="space-y-4">
          <p class="text-sm text-gray-600">选择要创建的任务类型：</p>
          <div class="grid grid-cols-2 gap-3">
            <Button variant="outline" @click="createOrder">
              <FileText class="w-4 h-4 mr-2" />
              新建订单
            </Button>
            <Button variant="outline" @click="createCuttingTask">
              <Scissors class="w-4 h-4 mr-2" />
              排版任务
            </Button>
            <Button variant="outline" @click="createProductionBatch">
              <Factory class="w-4 h-4 mr-2" />
              生产批次
            </Button>
            <Button variant="outline" @click="createQualityCheck">
              <CheckCircle class="w-4 h-4 mr-2" />
              质检任务
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Plus,
  RefreshCw,
  FileText,
  Scissors,
  Factory,
  TrendingUp,
  Calendar,
  CheckCircle,
  LayoutGrid,
  Zap,
  PanelRight,
  PanelsTopLeft,
  CalendarCheck2,
  GitBranch,
  ArrowRight,
  ClipboardList,
} from 'lucide-vue-next'

const router = useRouter()
const showCreateDialog = ref(false)

// 系统统计数据
const systemStats = ref({
  activeOrders: 24,
  newOrdersThisMonth: 8,
  productionOrders: 18,
  cuttingTasks: 15,
  avgUtilization: 85,
  productionBatches: 32,
  activeBatches: 12,
  equipmentUtilization: 78,
  utilizationImprovement: 12,
})

// 各模块统计数据
const orderStats = ref({
  confirmed: 8,
  inProgress: 12,
  completed: 4,
})

const productionOrderStats = ref({
  pending: 6,
  inProgress: 10,
  urgent: 2,
})

const cuttingStats = ref({
  pending: 5,
  completed: 10,
  avgUtilization: 85,
})

const productionStats = ref({
  planned: 8,
  inProgress: 12,
  utilization: 78,
})

const deliveryStats = ref({
  onTimeRate: 88,
  warnings: 3,
  satisfaction: 4.6,
})

const qualityStats = ref({
  passRate: 96.5,
  pending: 6,
  defective: 2,
})

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const refreshData = () => {
  // 刷新统计数据
  console.log('刷新系统数据')
}

const createOrder = () => {
  showCreateDialog.value = false
  router.push('/mes/orders')
}

const createCuttingTask = () => {
  showCreateDialog.value = false
  router.push('/mes/cutting-optimization')
}

const createProductionBatch = () => {
  showCreateDialog.value = false
  router.push('/mes/production-scheduling')
}

const createQualityCheck = () => {
  showCreateDialog.value = false
  router.push('/mes/quality-management')
}

// 生命周期
onMounted(() => {
  // 加载系统统计数据
  console.log('MES系统初始化完成')
})
</script>