<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- 页面标题和操作栏 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">生产工单管理</h1>
        <p class="text-gray-600 mt-1">管理已转换的生产工单，驱动生产执行和工段协调</p>
      </div>
      <div class="flex gap-3">
        <Button @click="refreshWorkOrders" variant="outline">
          <RefreshCw class="w-4 h-4 mr-2" />
          刷新
        </Button>
        <Button @click="$router.push('/test-production-order')" variant="outline" class="text-xs">
          测试界面
        </Button>
        <Button @click="showCreateDialog = true" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg">
          <Plus class="w-4 h-4 mr-2" />
          智能创建工单
          <span class="ml-2 text-xs bg-white/20 px-2 py-0.5 rounded-full">NEW</span>
        </Button>
      </div>
    </div>

    <!-- 数据流转概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">待发布工单</p>
              <p class="text-2xl font-bold text-blue-600">{{ workOrderStats.pending }}</p>
            </div>
            <Clock class="w-8 h-8 text-blue-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">已转换工单</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">执行中工单</p>
              <p class="text-2xl font-bold text-orange-600">{{ workOrderStats.inProgress }}</p>
            </div>
            <Factory class="w-8 h-8 text-orange-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">→ 排版任务</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">已完成工单</p>
              <p class="text-2xl font-bold text-green-600">{{ workOrderStats.completed }}</p>
            </div>
            <CheckCircle class="w-8 h-8 text-green-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">→ 成品交付</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">紧急工单</p>
              <p class="text-2xl font-bold text-red-600">{{ workOrderStats.urgent }}</p>
            </div>
            <AlertTriangle class="w-8 h-8 text-red-500" />
          </div>
          <p class="text-xs text-gray-500 mt-2">优先处理</p>
        </CardContent>
      </Card>
    </div>

    <!-- 筛选和搜索 -->
    <Card>
      <CardContent class="p-4">
        <div class="flex flex-wrap gap-4 items-center">
          <div class="flex-1 min-w-64">
            <Input
              v-model="searchQuery"
              placeholder="搜索工单号、客户名称..."
              class="w-full"
            />
          </div>
          <Select v-model="statusFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="工单状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="pending">待发布</SelectItem>
              <SelectItem value="released">已发布</SelectItem>
              <SelectItem value="in_progress">执行中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
            </SelectContent>
          </Select>
          <Select v-model="priorityFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="优先级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部优先级</SelectItem>
              <SelectItem value="urgent">紧急</SelectItem>
              <SelectItem value="high">高</SelectItem>
              <SelectItem value="normal">普通</SelectItem>
              <SelectItem value="low">低</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>

    <!-- 生产工单列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <FileText class="w-5 h-5" />
          生产工单列表
          <Badge variant="secondary">{{ filteredWorkOrders.length }} 个工单</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="workOrder in filteredWorkOrders"
            :key="workOrder.id"
            class="border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
            @click="selectWorkOrder(workOrder)"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <h3 class="font-semibold text-lg">{{ workOrder.workOrderNumber }}</h3>
                  <Badge :variant="getStatusVariant(workOrder.status)">
                    {{ getStatusText(workOrder.status) }}
                  </Badge>
                  <Badge :variant="getPriorityVariant(workOrder.priority)">
                    {{ getPriorityText(workOrder.priority) }}
                  </Badge>
                  <Badge variant="outline">{{ workOrder.customerName }}</Badge>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                  <div>
                    <span class="font-medium">客户订单：</span>
                    <Button 
                      variant="link" 
                      size="sm" 
                      class="p-0 h-auto text-blue-600"
                      @click.stop="jumpToCustomerOrder(workOrder.customerOrderId)"
                    >
                      {{ workOrder.customerOrderNumber }}
                    </Button>
                  </div>
                  <div>
                    <span class="font-medium">工单明细：</span>
                    {{ workOrder.items.length }}个
                  </div>
                  <div>
                    <span class="font-medium">计划完成：</span>
                    {{ formatDate(workOrder.plannedEndDate) }}
                  </div>
                </div>

                <!-- 当前工序状态 -->
                <div class="mb-3">
                  <p class="text-sm font-medium text-gray-700 mb-2">当前工序状态：</p>
                  <div class="flex flex-wrap gap-2">
                    <div
                      v-for="item in workOrder.items.slice(0, 3)"
                      :key="item.id"
                      class="text-xs px-2 py-1 rounded"
                      :class="getWorkstationStatusClass(item.currentStatus)"
                    >
                      {{ item.specifications.length }}×{{ item.specifications.width }}mm - {{ item.currentStatus }}
                    </div>
                    <div v-if="workOrder.items.length > 3" class="text-xs text-gray-500 px-2 py-1">
                      +{{ workOrder.items.length - 3 }}个明细
                    </div>
                  </div>
                </div>

                <!-- 数据流转链路展示 -->
                <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                  <p class="text-sm font-medium text-blue-800 mb-2">数据流转链路：</p>
                  <div class="flex items-center gap-2 text-xs">
                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded">客户订单</span>
                    <ArrowRight class="w-3 h-3 text-green-500" />
                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded">生产工单</span>
                    <ArrowRight class="w-3 h-3 text-blue-500" />
                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded">排版任务</span>
                    <ArrowRight class="w-3 h-3 text-orange-500" />
                    <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded">生产批次</span>
                  </div>
                </div>
              </div>
              
              <div class="flex flex-col items-end gap-2">
                <Button size="sm" variant="outline" @click.stop="viewWorkOrderDetails(workOrder)">
                  <Eye class="w-4 h-4 mr-1" />
                  查看详情
                </Button>
                <Button 
                  v-if="workOrder.status === 'pending'"
                  size="sm" 
                  @click.stop="releaseWorkOrder(workOrder)"
                >
                  <Play class="w-4 h-4 mr-1" />
                  发布工单
                </Button>
                <Button 
                  v-if="workOrder.status === 'released'"
                  size="sm" 
                  variant="outline"
                  @click.stop="createCuttingTask(workOrder)"
                >
                  <Scissors class="w-4 h-4 mr-1" />
                  创建排版
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 工单详情对话框 -->
    <Dialog v-model:open="showWorkOrderDetails">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>生产工单详情 - {{ selectedWorkOrder?.workOrderNumber }}</DialogTitle>
        </DialogHeader>
        
        <div v-if="selectedWorkOrder" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-sm font-medium">工单号</Label>
              <p class="text-sm text-gray-600">{{ selectedWorkOrder.workOrderNumber }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">客户订单</Label>
              <Button 
                variant="link" 
                size="sm" 
                class="p-0 h-auto text-blue-600"
                @click="jumpToCustomerOrder(selectedWorkOrder.customerOrderId)"
              >
                {{ selectedWorkOrder.customerOrderNumber }}
              </Button>
            </div>
            <div>
              <Label class="text-sm font-medium">客户名称</Label>
              <p class="text-sm text-gray-600">{{ selectedWorkOrder.customerName }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">优先级</Label>
              <Badge :variant="getPriorityVariant(selectedWorkOrder.priority)">
                {{ getPriorityText(selectedWorkOrder.priority) }}
              </Badge>
            </div>
            <div>
              <Label class="text-sm font-medium">计划开始</Label>
              <p class="text-sm text-gray-600">{{ formatDate(selectedWorkOrder.plannedStartDate) }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium">计划完成</Label>
              <p class="text-sm text-gray-600">{{ formatDate(selectedWorkOrder.plannedEndDate) }}</p>
            </div>
          </div>

          <!-- 工单明细 -->
          <div>
            <h4 class="font-medium mb-3">工单明细</h4>
            <div class="space-y-3">
              <div
                v-for="item in selectedWorkOrder.items"
                :key="item.id"
                class="border rounded-lg p-3"
              >
                <div class="flex justify-between items-start mb-2">
                  <div>
                    <p class="font-medium">
                      {{ item.specifications.length }}×{{ item.specifications.width }}×{{ item.specifications.thickness }}mm
                    </p>
                    <p class="text-sm text-gray-600">
                      {{ item.specifications.glassType }} {{ item.specifications.color }} × {{ item.quantity }}片
                    </p>
                  </div>
                  <div class="text-right">
                    <Badge variant="outline">{{ item.currentStatus }}</Badge>
                    <p class="text-xs text-gray-500 mt-1">{{ item.currentWorkstation }}</p>
                  </div>
                </div>
                
                <!-- 工艺流程 -->
                <div class="mt-3">
                  <p class="text-sm font-medium mb-2">工艺流程：</p>
                  <div class="flex items-center gap-2 text-xs">
                    <span
                      v-for="(step, index) in item.processFlow"
                      :key="index"
                      class="px-2 py-1 rounded"
                      :class="getStepStatusClass(step.status)"
                    >
                      {{ step.stepName }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 新建工单对话框 -->
    <ProductionOrderCreationDialog
      :open="showCreateDialog"
      @update:open="showCreateDialog = $event"
      @order-created="handleOrderCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Plus,
  RefreshCw,
  Clock,
  Factory,
  CheckCircle,
  AlertTriangle,
  FileText,
  Eye,
  ArrowRight,
  Play,
  Scissors,
} from 'lucide-vue-next'

import type { ProductionOrder, CustomerOrder } from '@/types/mes-validation'
import { mesService } from '@/services/mesService'
import ProductionOrderCreationDialog from '@/components/mes/ProductionOrderCreationDialog.vue'

const router = useRouter()

// 响应式数据
const workOrders = ref<ProductionOrder[]>([])
const searchQuery = ref('')
const statusFilter = ref('all')
const priorityFilter = ref('all')
const showWorkOrderDetails = ref(false)
const showCreateDialog = ref(false)
const selectedWorkOrder = ref<ProductionOrder | null>(null)

// 计算属性
const workOrderStats = computed(() => {
  const stats = {
    pending: 0,
    released: 0,
    inProgress: 0,
    completed: 0,
    urgent: 0,
  }
  
  workOrders.value.forEach(workOrder => {
    switch (workOrder.status) {
      case 'pending':
        stats.pending++
        break
      case 'released':
        stats.released++
        break
      case 'in_progress':
        stats.inProgress++
        break
      case 'completed':
        stats.completed++
        break
    }
    
    if (workOrder.priority === 'urgent') {
      stats.urgent++
    }
  })
  
  return stats
})

const filteredWorkOrders = computed(() => {
  return workOrders.value.filter(workOrder => {
    const matchesSearch = !searchQuery.value || 
      workOrder.workOrderNumber.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      workOrder.customerName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      workOrder.customerOrderNumber.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = statusFilter.value === 'all' || workOrder.status === statusFilter.value
    const matchesPriority = priorityFilter.value === 'all' || workOrder.priority === priorityFilter.value
    
    return matchesSearch && matchesStatus && matchesPriority
  })
})

// 方法
const loadWorkOrders = async () => {
  try {
    workOrders.value = await mesService.getProductionOrders()
  } catch (error) {
    console.error('加载生产工单失败:', error)
  }
}

const handleOrderCreated = (orderIds: string[]) => {
  console.log('工单创建成功:', orderIds)
  // 刷新工单列表
  loadWorkOrders()
  
  // 显示成功提示
  if (orderIds.length === 1) {
    alert(`工单创建成功！工单号: ${orderIds[0]}`)
  } else {
    alert(`批量工单创建成功！共创建 ${orderIds.length} 个工单`)
  }
}

const refreshWorkOrders = () => {
  loadWorkOrders()
}

const selectWorkOrder = (workOrder: ProductionOrder) => {
  selectedWorkOrder.value = workOrder
}

const viewWorkOrderDetails = (workOrder: ProductionOrder) => {
  selectedWorkOrder.value = workOrder
  showWorkOrderDetails.value = true
}

const releaseWorkOrder = async (workOrder: ProductionOrder) => {
  await mesService.updateProductionOrderStatus(workOrder.id, 'released')
  workOrder.status = 'released'
}

const createCuttingTask = (workOrder: ProductionOrder) => {
  // 跳转到排版优化界面，并传递工单信息
  router.push(`/mes/cutting-optimization?fromWorkOrder=${workOrder.id}`)
}



const jumpToCustomerOrder = (customerOrderId: string) => {
  // 这里可以跳转到客户订单详情或CRM系统
  console.log('跳转到客户订单:', customerOrderId)
}

// 状态相关方法
const getStatusVariant = (status: string) => {
  switch (status) {
    case 'pending': return 'secondary'
    case 'released': return 'default'
    case 'in_progress': return 'default'
    case 'completed': return 'default'
    case 'cancelled': return 'destructive'
    default: return 'secondary'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待发布'
    case 'released': return '已发布'
    case 'in_progress': return '执行中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return status
  }
}

const getPriorityVariant = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'destructive'
    case 'high': return 'default'
    case 'normal': return 'secondary'
    case 'low': return 'outline'
    default: return 'secondary'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'urgent': return '紧急'
    case 'high': return '高'
    case 'normal': return '普通'
    case 'low': return '低'
    default: return priority
  }
}

const getWorkstationStatusClass = (status: string) => {
  switch (status) {
    case '待排版': return 'bg-blue-100 text-blue-700'
    case '排版中': return 'bg-orange-100 text-orange-700'
    case '生产中': return 'bg-purple-100 text-purple-700'
    case '质检中': return 'bg-yellow-100 text-yellow-700'
    case '已完成': return 'bg-green-100 text-green-700'
    default: return 'bg-gray-100 text-gray-700'
  }
}

const getStepStatusClass = (status: string) => {
  switch (status) {
    case 'pending': return 'bg-gray-100 text-gray-700'
    case 'in_progress': return 'bg-blue-100 text-blue-700'
    case 'completed': return 'bg-green-100 text-green-700'
    case 'skipped': return 'bg-yellow-100 text-yellow-700'
    default: return 'bg-gray-100 text-gray-700'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(async () => {
  await loadWorkOrders()
})
</script>