import type { RouteRecordRaw } from 'vue-router'

/**
 * 路由模块配置文件
 * 支持按功能模块扩展路由配置 (Requirement 6.3)
 */

// 路由模块接口定义
export interface RouteModule {
  name: string
  routes: RouteRecordRaw[]
  meta?: {
    title: string
    description?: string
    order?: number
  }
}

// CRM模块路由配置
export const crmModule: RouteModule = {
  name: 'crm',
  meta: {
    title: '客户关系管理',
    description: '客户信息、订单管理、销售跟进',
    order: 1
  },
  routes: [
    {
      path: '/crm',
      name: 'crm',
      component: () => import('../views/CrmView.vue'),
      meta: {
        title: '客户关系管理',
        icon: 'Users',
        requiresAuth: true
      }
    },
    {
      path: '/crm/customers',
      name: 'crm-customers',
      component: () => import('../views/crm/CustomersView.vue'),
      meta: {
        title: '客户管理',
        icon: 'User',
        requiresAuth: true,
        parent: 'crm'
      }
    },
    {
      path: '/crm/orders',
      name: 'crm-orders',
      component: () => import('../views/crm/OrdersView.vue'),
      meta: {
        title: '订单管理',
        icon: 'FileText',
        requiresAuth: true,
        parent: 'crm'
      }
    }
  ]
}

// 库存管理模块路由配置
export const inventoryModule: RouteModule = {
  name: 'inventory',
  meta: {
    title: '库存管理',
    description: '物料管理、库存监控、变体配置',
    order: 2
  },
  routes: [
    {
      path: '/inventory',
      name: 'inventory',
      component: () => import('../views/InventoryView.vue'),
      meta: {
        title: '库存管理',
        icon: 'Package',
        requiresAuth: true
      }
    },
    {
      path: '/inventory/materials',
      name: 'inventory-materials',
      component: () => import('../views/inventory/MaterialsView.vue'),
      meta: {
        title: '物料管理',
        icon: 'Box',
        requiresAuth: true,
        parent: 'inventory'
      }
    },
    {
      path: '/inventory/material-variants',
      name: 'material-variants',
      component: () => import('../views/MaterialVariantView.vue'),
      meta: {
        title: '物料变体管理',
        icon: 'Package2',
        requiresAuth: true,
        parent: 'inventory'
      }
    },
    {
      path: '/inventory/stock',
      name: 'inventory-stock',
      component: () => import('../views/inventory/StockView.vue'),
      meta: {
        title: '库存监控',
        icon: 'BarChart3',
        requiresAuth: true,
        parent: 'inventory'
      }
    }
  ]
}

// 生产管理模块路由配置
export const productionModule: RouteModule = {
  name: 'production',
  meta: {
    title: '生产管理',
    description: '生产计划、工艺管理、设备监控',
    order: 3
  },
  routes: [
    {
      path: '/mes',
      name: 'mes',
      component: () => import('../views/MesView.vue'),
      meta: {
        title: '生产执行系统',
        icon: 'Factory',
        requiresAuth: true
      }
    },
    {
      path: '/mes/planning',
      name: 'mes-planning',
      component: () => import('../views/mes/PlanningView.vue'),
      meta: {
        title: '生产计划',
        icon: 'Calendar',
        requiresAuth: true,
        parent: 'mes'
      }
    },
    {
      path: '/mes/processes',
      name: 'mes-processes',
      component: () => import('../views/mes/ProcessesView.vue'),
      meta: {
        title: '工艺管理',
        icon: 'Settings',
        requiresAuth: true,
        parent: 'mes'
      }
    }
  ]
}

// 采购管理模块路由配置
export const procurementModule: RouteModule = {
  name: 'procurement',
  meta: {
    title: '采购管理',
    description: '供应商管理、采购订单、入库管理',
    order: 4
  },
  routes: [
    {
      path: '/procurement',
      name: 'procurement',
      component: () => import('../views/ProcurementView.vue'),
      meta: {
        title: '采购管理',
        icon: 'ShoppingCart',
        requiresAuth: true
      }
    },
    {
      path: '/procurement/suppliers',
      name: 'procurement-suppliers',
      component: () => import('../views/procurement/SuppliersView.vue'),
      meta: {
        title: '供应商管理',
        icon: 'Truck',
        requiresAuth: true,
        parent: 'procurement'
      }
    },
    {
      path: '/procurement/orders',
      name: 'procurement-orders',
      component: () => import('../views/procurement/OrdersView.vue'),
      meta: {
        title: '采购订单',
        icon: 'FileText',
        requiresAuth: true,
        parent: 'procurement'
      }
    }
  ]
}

// 质量管理模块路由配置
export const qualityModule: RouteModule = {
  name: 'quality',
  meta: {
    title: '质量管理',
    description: '质量检测、不合格品处理、质量分析',
    order: 5
  },
  routes: [
    {
      path: '/quality',
      name: 'quality',
      component: () => import('../views/QualityView.vue'),
      meta: {
        title: '质量管理',
        icon: 'CheckCircle',
        requiresAuth: true
      }
    },
    {
      path: '/quality/inspections',
      name: 'quality-inspections',
      component: () => import('../views/quality/InspectionsView.vue'),
      meta: {
        title: '质量检测',
        icon: 'Search',
        requiresAuth: true,
        parent: 'quality'
      }
    },
    {
      path: '/quality/analysis',
      name: 'quality-analysis',
      component: () => import('../views/quality/AnalysisView.vue'),
      meta: {
        title: '质量分析',
        icon: 'TrendingUp',
        requiresAuth: true,
        parent: 'quality'
      }
    }
  ]
}

// 所有模块配置
export const routeModules: RouteModule[] = [
  crmModule,
  inventoryModule,
  productionModule,
  procurementModule,
  qualityModule
]

// 获取所有模块路由
export const getAllModuleRoutes = (): RouteRecordRaw[] => {
  return routeModules.reduce((routes, module) => {
    return [...routes, ...module.routes]
  }, [] as RouteRecordRaw[])
}

// 根据模块名获取路由
export const getModuleRoutes = (moduleName: string): RouteRecordRaw[] => {
  const module = routeModules.find(m => m.name === moduleName)
  return module ? module.routes : []
}

// 获取模块信息
export const getModuleInfo = (moduleName: string): RouteModule | undefined => {
  return routeModules.find(m => m.name === moduleName)
}