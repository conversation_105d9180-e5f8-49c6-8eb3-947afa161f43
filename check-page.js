// 简单的页面检查脚本
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 5173,
  path: '/',
  method: 'GET'
};

const req = http.request(options, (res) => {
  console.log(`状态码: ${res.statusCode}`);
  console.log(`响应头: ${JSON.stringify(res.headers)}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('页面内容长度:', data.length);
    
    // 检查是否包含关键的 CSS 类
    const hasStyles = data.includes('bg-background') || data.includes('sidebar') || data.includes('tailwind');
    console.log('包含样式类:', hasStyles);
    
    // 检查是否有错误
    const hasErrors = data.includes('error') || data.includes('Error') || data.includes('404');
    console.log('包含错误:', hasErrors);
    
    // 输出前500个字符
    console.log('\n页面开头内容:');
    console.log(data.substring(0, 500));
  });
});

req.on('error', (e) => {
  console.error(`请求遇到问题: ${e.message}`);
});

req.end();