{"productTemplates": [{"id": "template_fire_window_standard", "name": "标准防火窗", "code": "FIRE_WINDOW_STD", "category": "门窗", "productType": "window", "description": "标准防火窗产品模板，支持参数化配置，包含完整的防火窗结构组件", "structure": {"id": "structure_fire_window_standard", "name": "标准防火窗结构", "components": [{"id": "component_outer_frame", "name": "外框", "type": "frame", "description": "防火窗外框结构，承载整个窗体", "parameters": [{"id": "outer_frame_width", "name": "外框宽度", "type": "dimension", "dataType": "formula", "formula": "window_width + frame_thickness * 2", "constraints": [], "unit": "mm", "description": "外框总宽度"}, {"id": "outer_frame_height", "name": "外框高度", "type": "dimension", "dataType": "formula", "formula": "window_height + frame_thickness * 2", "constraints": [], "unit": "mm", "description": "外框总高度"}, {"id": "outer_frame_perimeter", "name": "外框周长", "type": "dimension", "dataType": "formula", "formula": "(outer_frame_width + outer_frame_height) * 2", "constraints": [], "unit": "mm", "description": "外框型材总长度"}], "subComponents": [{"id": "subcomp_outer_frame_profile", "name": "外框型材", "materialType": "profile", "parameters": [{"id": "outer_frame_profile_spec", "name": "外框型材规格", "type": "material_property", "dataType": "formula", "formula": "frame_thickness + 'x' + frame_depth", "unit": "mm", "description": "外框型材截面规格"}], "quantityFormula": "ceil(outer_frame_perimeter / 6000)", "materialSelectionRules": [{"id": "rule_outer_frame_profile", "condition": "fire_rating >= 'A' && frame_thickness >= 50", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "window_frame", "thickness": {"min": 50}, "material": "steel"}, "preferredProperties": {"surface_treatment": "galvanized", "strength_grade": "Q235"}}, "priority": 1}]}, {"id": "subcomp_frame_corners", "name": "框角连接件", "materialType": "hardware", "parameters": [], "quantityFormula": "4", "materialSelectionRules": [{"id": "rule_frame_corners", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "type": "corner_connector", "material": "steel"}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": true}, {"id": "component_middle_horizontal_frame", "name": "中横框", "type": "frame", "description": "防火窗中横框，用于分隔上下窗扇", "parameters": [{"id": "middle_h_frame_length", "name": "中横框长度", "type": "dimension", "dataType": "formula", "formula": "window_width - frame_thickness * 2", "constraints": [], "unit": "mm", "description": "中横框长度"}], "subComponents": [{"id": "subcomp_middle_h_profile", "name": "中横框型材", "materialType": "profile", "parameters": [], "quantityFormula": "ceil(middle_h_frame_length / 6000) * middle_horizontal_count", "materialSelectionRules": [{"id": "rule_middle_h_profile", "condition": "fire_rating >= 'A' && has_middle_horizontal == true", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "mullion", "material": "steel"}}, "priority": 1}]}], "quantityFormula": "has_middle_horizontal ? middle_horizontal_count : 0", "isRequired": false, "isConfigurable": true}, {"id": "component_middle_vertical_frame", "name": "中竖框", "type": "frame", "description": "防火窗中竖框，用于分隔左右窗扇", "parameters": [{"id": "middle_v_frame_length", "name": "中竖框长度", "type": "dimension", "dataType": "formula", "formula": "window_height - frame_thickness * 2", "constraints": [], "unit": "mm", "description": "中竖框长度"}], "subComponents": [{"id": "subcomp_middle_v_profile", "name": "中竖框型材", "materialType": "profile", "parameters": [], "quantityFormula": "ceil(middle_v_frame_length / 6000) * middle_vertical_count", "materialSelectionRules": [{"id": "rule_middle_v_profile", "condition": "fire_rating >= 'A' && has_middle_vertical == true", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "transom", "material": "steel"}}, "priority": 1}]}], "quantityFormula": "has_middle_vertical ? middle_vertical_count : 0", "isRequired": false, "isConfigurable": true}, {"id": "component_left_sash", "name": "左窗扇", "type": "sash", "description": "防火窗左侧窗扇", "parameters": [{"id": "left_sash_width", "name": "左扇宽度", "type": "dimension", "dataType": "formula", "formula": "has_middle_vertical ? (window_width - frame_thickness * 2 - middle_v_frame_width) / 2 : window_width / sash_count - sash_gap", "constraints": [], "unit": "mm"}, {"id": "left_sash_height", "name": "左扇高度", "type": "dimension", "dataType": "formula", "formula": "has_middle_horizontal ? (window_height - frame_thickness * 2 - middle_h_frame_width) / 2 : window_height - frame_thickness * 2 - sash_gap * 2", "constraints": [], "unit": "mm"}, {"id": "left_sash_perimeter", "name": "左扇周长", "type": "dimension", "dataType": "formula", "formula": "(left_sash_width + left_sash_height) * 2", "constraints": [], "unit": "mm"}], "subComponents": [{"id": "subcomp_left_sash_profile", "name": "左扇型材", "materialType": "profile", "parameters": [], "quantityFormula": "ceil(left_sash_perimeter / 6000)", "materialSelectionRules": [{"id": "rule_left_sash_profile", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "sash", "material": "steel"}}, "priority": 1}]}], "quantityFormula": "sash_count >= 2 ? 1 : 0", "isRequired": false, "isConfigurable": true}, {"id": "component_right_sash", "name": "右窗扇", "type": "sash", "description": "防火窗右侧窗扇", "parameters": [{"id": "right_sash_width", "name": "右扇宽度", "type": "dimension", "dataType": "formula", "formula": "has_middle_vertical ? (window_width - frame_thickness * 2 - middle_v_frame_width) / 2 : window_width / sash_count - sash_gap", "constraints": [], "unit": "mm"}, {"id": "right_sash_height", "name": "右扇高度", "type": "dimension", "dataType": "formula", "formula": "has_middle_horizontal ? (window_height - frame_thickness * 2 - middle_h_frame_width) / 2 : window_height - frame_thickness * 2 - sash_gap * 2", "constraints": [], "unit": "mm"}, {"id": "right_sash_perimeter", "name": "右扇周长", "type": "dimension", "dataType": "formula", "formula": "(right_sash_width + right_sash_height) * 2", "constraints": [], "unit": "mm"}], "subComponents": [{"id": "subcomp_right_sash_profile", "name": "右扇型材", "materialType": "profile", "parameters": [], "quantityFormula": "ceil(right_sash_perimeter / 6000)", "materialSelectionRules": [{"id": "rule_right_sash_profile", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "sash", "material": "steel"}}, "priority": 1}]}], "quantityFormula": "sash_count >= 2 ? 1 : 0", "isRequired": false, "isConfigurable": true}, {"id": "component_fire_glass", "name": "防火玻璃", "type": "glass", "description": "防火窗专用防火玻璃", "parameters": [{"id": "total_glass_area", "name": "玻璃总面积", "type": "dimension", "dataType": "formula", "formula": "left_glass_area + right_glass_area", "constraints": [], "unit": "m²"}, {"id": "left_glass_area", "name": "左扇玻璃面积", "type": "dimension", "dataType": "formula", "formula": "(left_sash_width - glass_margin * 2) * (left_sash_height - glass_margin * 2) / 1000000", "constraints": [], "unit": "m²"}, {"id": "right_glass_area", "name": "右扇玻璃面积", "type": "dimension", "dataType": "formula", "formula": "(right_sash_width - glass_margin * 2) * (right_sash_height - glass_margin * 2) / 1000000", "constraints": [], "unit": "m²"}], "subComponents": [{"id": "subcomp_left_fire_glass", "name": "左扇防火玻璃", "materialType": "glass", "parameters": [], "quantityFormula": "left_glass_area", "materialSelectionRules": [{"id": "rule_left_fire_glass", "condition": "fire_rating >= 'A' && glass_thickness >= 6", "materialCriteria": {"materialType": "glass", "requiredProperties": {"fire_rating": "A", "glass_type": "fire_resistant", "thickness": {"min": 6}}, "preferredProperties": {"transparency": "clear", "fire_resistance_time": {"min": 60}}}, "priority": 1}]}, {"id": "subcomp_right_fire_glass", "name": "右扇防火玻璃", "materialType": "glass", "parameters": [], "quantityFormula": "right_glass_area", "materialSelectionRules": [{"id": "rule_right_fire_glass", "condition": "fire_rating >= 'A' && glass_thickness >= 6", "materialCriteria": {"materialType": "glass", "requiredProperties": {"fire_rating": "A", "glass_type": "fire_resistant", "thickness": {"min": 6}}, "preferredProperties": {"transparency": "clear", "fire_resistance_time": {"min": 60}}}, "priority": 1}]}, {"id": "subcomp_glass_sealant", "name": "玻璃密封胶", "materialType": "sealant", "parameters": [], "quantityFormula": "((left_sash_width + left_sash_height) * 2 + (right_sash_width + right_sash_height) * 2) / 1000", "materialSelectionRules": [{"id": "rule_glass_sealant", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "sealant", "requiredProperties": {"fire_rating": "A", "sealant_type": "structural", "temperature_resistance": {"min": 200}}}, "priority": 1}]}], "quantityFormula": "sash_count", "isRequired": true, "isConfigurable": true}, {"id": "component_hardware_system", "name": "五金件系统", "type": "hardware", "description": "防火窗五金配件系统", "parameters": [{"id": "hardware_load_capacity", "name": "五金承重能力", "type": "structural_property", "dataType": "formula", "formula": "total_glass_area * glass_weight_per_sqm + sash_weight_estimate", "constraints": [], "unit": "kg", "description": "五金件需要承受的总重量"}], "subComponents": [{"id": "subcomp_hinges", "name": "合页", "materialType": "hardware", "parameters": [], "quantityFormula": "sash_count * hinges_per_sash", "materialSelectionRules": [{"id": "rule_hinges", "condition": "fire_rating >= 'A' && hardware_load_capacity <= hinge_capacity", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "hinge", "material": "stainless_steel", "load_capacity": {"min": 50}}}, "priority": 1}]}, {"id": "subcomp_handles", "name": "执手", "materialType": "hardware", "parameters": [], "quantityFormula": "sash_count", "materialSelectionRules": [{"id": "rule_handles", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "handle", "material": "stainless_steel", "operation_type": "lever"}}, "priority": 1}]}, {"id": "subcomp_locks", "name": "锁具", "materialType": "hardware", "parameters": [], "quantityFormula": "sash_count", "materialSelectionRules": [{"id": "rule_locks", "condition": "fire_rating >= 'A' && security_level >= 'standard'", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "lock", "security_grade": "A", "material": "stainless_steel"}}, "priority": 1}]}, {"id": "subcomp_weather_strips", "name": "密封条", "materialType": "sealant", "parameters": [], "quantityFormula": "((left_sash_perimeter + right_sash_perimeter) * 2 + outer_frame_perimeter) / 1000", "materialSelectionRules": [{"id": "rule_weather_strips", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "sealant", "requiredProperties": {"fire_rating": "A", "sealant_type": "weather_strip", "material": "epdm_rubber", "temperature_resistance": {"min": 150}}}, "priority": 1}]}, {"id": "subcomp_closing_device", "name": "闭窗器", "materialType": "hardware", "parameters": [], "quantityFormula": "has_auto_closing ? sash_count : 0", "materialSelectionRules": [{"id": "rule_closing_device", "condition": "fire_rating >= 'A' && has_auto_closing == true", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "closer", "activation_temperature": {"max": 70}}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": false}]}, "configurationRules": [{"id": "rule_basic_dimensions", "name": "基本尺寸配置", "type": "dimension", "parameters": [{"id": "window_width", "name": "窗宽", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 1500, "minValue": 600, "maxValue": 3000, "description": "防火窗净宽度"}, {"id": "window_height", "name": "窗高", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 1800, "minValue": 800, "maxValue": 2400, "description": "防火窗净高度"}, {"id": "sash_count", "name": "窗扇数量", "type": "dimension", "dataType": "select", "options": ["1", "2", "3", "4"], "defaultValue": "2", "description": "窗扇总数量"}], "constraints": [{"id": "constraint_max_area", "expression": "window_width * window_height <= 7200000", "errorMessage": "防火窗面积不能超过7.2平方米", "severity": "error"}, {"id": "constraint_aspect_ratio", "expression": "window_width / window_height >= 0.5 && window_width / window_height <= 3.0", "errorMessage": "窗户宽高比应在0.5-3.0之间", "severity": "warning"}], "isRequired": true}, {"id": "rule_fire_performance", "name": "防火性能配置", "type": "material", "parameters": [{"id": "fire_rating", "name": "防火等级", "type": "material_property", "dataType": "select", "options": ["A", "B", "C"], "defaultValue": "A", "description": "防火窗防火等级"}, {"id": "fire_resistance_time", "name": "耐火时间", "type": "material_property", "dataType": "select", "options": ["30", "60", "90", "120"], "defaultValue": "60", "unit": "分钟", "description": "防火窗耐火极限时间"}], "constraints": [{"id": "constraint_fire_rating_time", "expression": "(fire_rating == 'A' && fire_resistance_time >= 60) || (fire_rating == 'B' && fire_resistance_time >= 30)", "errorMessage": "A级防火窗耐火时间不少于60分钟，B级不少于30分钟", "severity": "error"}], "isRequired": true}, {"id": "rule_frame_structure", "name": "框架结构配置", "type": "dimension", "parameters": [{"id": "frame_thickness", "name": "框厚", "type": "dimension", "dataType": "select", "options": ["50", "60", "70", "80"], "defaultValue": "60", "unit": "mm", "description": "窗框型材厚度"}, {"id": "frame_depth", "name": "框深", "type": "dimension", "dataType": "select", "options": ["60", "70", "80", "90"], "defaultValue": "70", "unit": "mm", "description": "窗框型材深度"}, {"id": "has_middle_horizontal", "name": "是否有中横框", "type": "structural_property", "dataType": "boolean", "defaultValue": false, "description": "是否包含中横框分隔"}, {"id": "middle_horizontal_count", "name": "中横框数量", "type": "dimension", "dataType": "select", "options": ["0", "1", "2"], "defaultValue": "0", "description": "中横框数量"}, {"id": "has_middle_vertical", "name": "是否有中竖框", "type": "structural_property", "dataType": "boolean", "defaultValue": true, "description": "是否包含中竖框分隔"}, {"id": "middle_vertical_count", "name": "中竖框数量", "type": "dimension", "dataType": "select", "options": ["0", "1", "2"], "defaultValue": "1", "description": "中竖框数量"}, {"id": "middle_h_frame_width", "name": "中横框宽度", "type": "dimension", "dataType": "number", "defaultValue": 50, "unit": "mm", "description": "中横框型材宽度"}, {"id": "middle_v_frame_width", "name": "中竖框宽度", "type": "dimension", "dataType": "number", "defaultValue": 50, "unit": "mm", "description": "中竖框型材宽度"}], "constraints": [{"id": "constraint_middle_frame_logic", "expression": "(has_middle_horizontal == false && middle_horizontal_count == 0) || (has_middle_horizontal == true && middle_horizontal_count > 0)", "errorMessage": "中横框配置逻辑错误", "severity": "error"}], "isRequired": false}, {"id": "rule_sash_config", "name": "窗扇配置", "type": "dimension", "parameters": [{"id": "sash_gap", "name": "扇间隙", "type": "dimension", "dataType": "number", "defaultValue": 5, "minValue": 3, "maxValue": 10, "unit": "mm", "description": "窗扇与框的间隙"}, {"id": "sash_opening_type", "name": "开启方式", "type": "structural_property", "dataType": "select", "options": ["fixed", "casement", "sliding", "tilt_turn"], "defaultValue": "casement", "description": "窗扇开启方式"}], "constraints": [], "isRequired": false}, {"id": "rule_glass_config", "name": "玻璃配置", "type": "material", "parameters": [{"id": "glass_thickness", "name": "玻璃厚度", "type": "material_property", "dataType": "select", "options": ["6", "8", "10", "12", "15"], "defaultValue": "6", "unit": "mm", "description": "防火玻璃厚度"}, {"id": "glass_margin", "name": "玻璃边距", "type": "dimension", "dataType": "number", "defaultValue": 20, "minValue": 15, "maxValue": 30, "unit": "mm", "description": "玻璃与扇框的边距"}, {"id": "glass_weight_per_sqm", "name": "玻璃单位重量", "type": "material_property", "dataType": "formula", "formula": "glass_thickness * 2.5", "unit": "kg/m²", "description": "防火玻璃单位面积重量"}], "constraints": [{"id": "constraint_glass_fire_rating", "expression": "(fire_rating == 'A' && glass_thickness >= 6) || (fire_rating == 'B' && glass_thickness >= 5)", "errorMessage": "A级防火窗玻璃厚度不少于6mm，B级不少于5mm", "severity": "error"}], "isRequired": false}, {"id": "rule_hardware_config", "name": "五金配置", "type": "material", "parameters": [{"id": "hinges_per_sash", "name": "每扇合页数", "type": "dimension", "dataType": "select", "options": ["2", "3", "4"], "defaultValue": "3", "description": "每个窗扇的合页数量"}, {"id": "hinge_capacity", "name": "合页承重", "type": "material_property", "dataType": "select", "options": ["50", "80", "100", "120"], "defaultValue": "80", "unit": "kg", "description": "单个合页承重能力"}, {"id": "security_level", "name": "安全等级", "type": "material_property", "dataType": "select", "options": ["standard", "enhanced", "high"], "defaultValue": "standard", "description": "五金件安全等级"}, {"id": "has_auto_closing", "name": "自动闭窗", "type": "structural_property", "dataType": "boolean", "defaultValue": true, "description": "是否配置自动闭窗器"}, {"id": "sash_weight_estimate", "name": "窗扇重量估算", "type": "structural_property", "dataType": "formula", "formula": "((left_sash_perimeter + right_sash_perimeter) / 1000) * 2.5 + 10", "unit": "kg", "description": "窗扇型材重量估算"}], "constraints": [{"id": "constraint_hinge_capacity", "expression": "hinge_capacity * hinges_per_sash >= hardware_load_capacity / sash_count", "errorMessage": "合页承重能力不足", "severity": "warning"}], "isRequired": false}], "defaultBOMTemplateId": "bom_template_fire_window_standard", "defaultProcessRouteId": "process_route_fire_window_standard", "costCalculationRules": [{"id": "cost_rule_material", "name": "物料成本", "type": "material", "formula": "sum(bom_item_cost)", "parameters": ["bom_item_cost"], "isActive": true}, {"id": "cost_rule_labor", "name": "人工成本", "type": "labor", "formula": "material_cost * 0.35", "parameters": ["material_cost"], "isActive": true}, {"id": "cost_rule_overhead", "name": "制造费用", "type": "overhead", "formula": "material_cost * 0.15", "parameters": ["material_cost"], "isActive": true}], "isActive": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "template_fire_window_large", "name": "大型防火窗", "code": "FIRE_WINDOW_LARGE", "category": "门窗", "productType": "window", "description": "大型防火窗产品模板，适用于大面积防火窗，支持复杂分格结构", "structure": {"id": "structure_fire_window_large", "name": "大型防火窗结构", "components": [{"id": "component_main_frame", "name": "主框架", "type": "frame", "description": "大型防火窗主框架结构", "parameters": [{"id": "main_frame_width", "name": "主框宽度", "type": "dimension", "dataType": "formula", "formula": "window_width + frame_thickness * 2", "constraints": [], "unit": "mm"}, {"id": "main_frame_height", "name": "主框高度", "type": "dimension", "dataType": "formula", "formula": "window_height + frame_thickness * 2", "constraints": [], "unit": "mm"}], "subComponents": [{"id": "subcomp_main_frame_profile", "name": "主框型材", "materialType": "profile", "parameters": [], "quantityFormula": "ceil((main_frame_width + main_frame_height) * 2 / 6000)", "materialSelectionRules": [{"id": "rule_main_frame_profile", "condition": "fire_rating >= 'A' && frame_thickness >= 70", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "heavy_frame", "thickness": {"min": 70}, "material": "steel", "strength_grade": "Q345"}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": true}, {"id": "component_grid_system", "name": "分格系统", "type": "frame", "description": "大型防火窗分格框架系统", "parameters": [{"id": "horizontal_grids", "name": "水平分格数", "type": "dimension", "dataType": "formula", "formula": "ceil(window_height / max_grid_height)", "constraints": [], "unit": "个"}, {"id": "vertical_grids", "name": "垂直分格数", "type": "dimension", "dataType": "formula", "formula": "ceil(window_width / max_grid_width)", "constraints": [], "unit": "个"}, {"id": "total_grid_count", "name": "总分格数", "type": "dimension", "dataType": "formula", "formula": "horizontal_grids * vertical_grids", "constraints": [], "unit": "个"}], "subComponents": [{"id": "subcomp_horizontal_mullions", "name": "水平分格条", "materialType": "profile", "parameters": [], "quantityFormula": "ceil((horizontal_grids - 1) * window_width / 6000)", "materialSelectionRules": [{"id": "rule_horizontal_mullions", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "mullion", "material": "steel"}}, "priority": 1}]}, {"id": "subcomp_vertical_transoms", "name": "垂直分格条", "materialType": "profile", "parameters": [], "quantityFormula": "ceil((vertical_grids - 1) * window_height / 6000)", "materialSelectionRules": [{"id": "rule_vertical_transoms", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "transom", "material": "steel"}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": true}, {"id": "component_operable_sashes", "name": "开启扇", "type": "sash", "description": "大型防火窗开启扇", "parameters": [{"id": "operable_sash_width", "name": "开启扇宽度", "type": "dimension", "dataType": "formula", "formula": "window_width / vertical_grids - grid_frame_width", "constraints": [], "unit": "mm"}, {"id": "operable_sash_height", "name": "开启扇高度", "type": "dimension", "dataType": "formula", "formula": "window_height / horizontal_grids - grid_frame_width", "constraints": [], "unit": "mm"}], "subComponents": [{"id": "subcomp_operable_sash_profile", "name": "开启扇型材", "materialType": "profile", "parameters": [], "quantityFormula": "ceil((operable_sash_width + operable_sash_height) * 2 / 6000) * operable_sash_count", "materialSelectionRules": [{"id": "rule_operable_sash_profile", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "sash", "material": "steel"}}, "priority": 1}]}], "quantityFormula": "operable_sash_count", "isRequired": false, "isConfigurable": true}, {"id": "component_fixed_panels", "name": "固定面板", "type": "glass", "description": "大型防火窗固定玻璃面板", "parameters": [{"id": "fixed_panel_width", "name": "固定面板宽度", "type": "dimension", "dataType": "formula", "formula": "window_width / vertical_grids - grid_frame_width", "constraints": [], "unit": "mm"}, {"id": "fixed_panel_height", "name": "固定面板高度", "type": "dimension", "dataType": "formula", "formula": "window_height / horizontal_grids - grid_frame_width", "constraints": [], "unit": "mm"}, {"id": "fixed_panel_area", "name": "单个固定面板面积", "type": "dimension", "dataType": "formula", "formula": "fixed_panel_width * fixed_panel_height / 1000000", "constraints": [], "unit": "m²"}], "subComponents": [{"id": "subcomp_fixed_fire_glass", "name": "固定防火玻璃", "materialType": "glass", "parameters": [], "quantityFormula": "fixed_panel_area * fixed_panel_count", "materialSelectionRules": [{"id": "rule_fixed_fire_glass", "condition": "fire_rating >= 'A' && glass_thickness >= 8", "materialCriteria": {"materialType": "glass", "requiredProperties": {"fire_rating": "A", "glass_type": "fire_resistant", "thickness": {"min": 8}}, "preferredProperties": {"transparency": "clear", "fire_resistance_time": {"min": 90}}}, "priority": 1}]}], "quantityFormula": "fixed_panel_count", "isRequired": true, "isConfigurable": true}, {"id": "component_reinforcement_system", "name": "加强系统", "type": "frame", "description": "大型防火窗结构加强系统", "parameters": [{"id": "reinforcement_points", "name": "加强点数量", "type": "structural_property", "dataType": "formula", "formula": "ceil(window_width * window_height / 2000000)", "constraints": [], "unit": "个"}], "subComponents": [{"id": "subcomp_steel_reinforcement", "name": "钢结构加强件", "materialType": "profile", "parameters": [], "quantityFormula": "reinforcement_points * 2", "materialSelectionRules": [{"id": "rule_steel_reinforcement", "condition": "window_width * window_height > 4000000", "materialCriteria": {"materialType": "profile", "requiredProperties": {"material": "steel", "profile_type": "reinforcement", "strength_grade": "Q345"}}, "priority": 1}]}, {"id": "subcomp_anchor_bolts", "name": "锚固螺栓", "materialType": "hardware", "parameters": [], "quantityFormula": "reinforcement_points * 4", "materialSelectionRules": [{"id": "rule_anchor_bolts", "condition": "window_width * window_height > 4000000", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"hardware_type": "anchor_bolt", "material": "stainless_steel", "diameter": {"min": 12}}}, "priority": 1}]}], "quantityFormula": "window_width * window_height > 4000000 ? 1 : 0", "isRequired": false, "isConfigurable": true}, {"id": "component_advanced_hardware", "name": "高级五金系统", "type": "hardware", "description": "大型防火窗专用五金系统", "parameters": [{"id": "total_hardware_load", "name": "五金总负荷", "type": "structural_property", "dataType": "formula", "formula": "(fixed_panel_area * fixed_panel_count + operable_sash_width * operable_sash_height * operable_sash_count / 1000000) * glass_weight_per_sqm", "constraints": [], "unit": "kg"}], "subComponents": [{"id": "subcomp_heavy_duty_hinges", "name": "重型合页", "materialType": "hardware", "parameters": [], "quantityFormula": "operable_sash_count * heavy_hinges_per_sash", "materialSelectionRules": [{"id": "rule_heavy_duty_hinges", "condition": "fire_rating >= 'A' && total_hardware_load > 200", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "heavy_hinge", "material": "stainless_steel", "load_capacity": {"min": 150}}}, "priority": 1}]}, {"id": "subcomp_multi_point_locks", "name": "多点锁具", "materialType": "hardware", "parameters": [], "quantityFormula": "operable_sash_count", "materialSelectionRules": [{"id": "rule_multi_point_locks", "condition": "fire_rating >= 'A' && security_level >= 'enhanced'", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "multi_point_lock", "security_grade": "AA", "material": "stainless_steel"}}, "priority": 1}]}, {"id": "subcomp_automatic_closers", "name": "自动闭窗器", "materialType": "hardware", "parameters": [], "quantityFormula": "operable_sash_count", "materialSelectionRules": [{"id": "rule_automatic_closers", "condition": "fire_rating >= 'A' && has_auto_closing == true", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "automatic_closer", "activation_temperature": {"max": 70}, "closing_force": {"min": 100}}}, "priority": 1}]}, {"id": "subcomp_structural_sealant", "name": "结构密封胶", "materialType": "sealant", "parameters": [], "quantityFormula": "((main_frame_width + main_frame_height) * 2 + (horizontal_grids - 1) * window_width + (vertical_grids - 1) * window_height) / 1000", "materialSelectionRules": [{"id": "rule_structural_sealant", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "sealant", "requiredProperties": {"fire_rating": "A", "sealant_type": "structural", "temperature_resistance": {"min": 250}, "adhesion_strength": {"min": 0.6}}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": false}]}, "configurationRules": [{"id": "rule_large_window_dimensions", "name": "大型窗尺寸配置", "type": "dimension", "parameters": [{"id": "window_width", "name": "窗宽", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 4000, "minValue": 2000, "maxValue": 8000, "description": "大型防火窗宽度"}, {"id": "window_height", "name": "窗高", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 3000, "minValue": 1500, "maxValue": 6000, "description": "大型防火窗高度"}, {"id": "max_grid_width", "name": "最大分格宽度", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 1200, "minValue": 800, "maxValue": 1500, "description": "单个分格最大宽度"}, {"id": "max_grid_height", "name": "最大分格高度", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 1500, "minValue": 1000, "maxValue": 2000, "description": "单个分格最大高度"}], "constraints": [{"id": "constraint_large_window_area", "expression": "window_width * window_height <= 48000000", "errorMessage": "大型防火窗面积不能超过48平方米", "severity": "error"}, {"id": "constraint_grid_size", "expression": "max_grid_width * max_grid_height <= 3000000", "errorMessage": "单个分格面积不能超过3平方米", "severity": "error"}], "isRequired": true}, {"id": "rule_enhanced_fire_performance", "name": "增强防火性能", "type": "material", "parameters": [{"id": "fire_rating", "name": "防火等级", "type": "material_property", "dataType": "select", "options": ["A", "A+"], "defaultValue": "A", "description": "大型防火窗防火等级"}, {"id": "fire_resistance_time", "name": "耐火时间", "type": "material_property", "dataType": "select", "options": ["60", "90", "120", "180"], "defaultValue": "90", "unit": "分钟", "description": "大型防火窗耐火极限时间"}], "constraints": [{"id": "constraint_enhanced_fire_rating", "expression": "fire_rating == 'A+' && fire_resistance_time >= 90", "errorMessage": "A+级大型防火窗耐火时间不少于90分钟", "severity": "error"}], "isRequired": true}, {"id": "rule_heavy_frame_config", "name": "重型框架配置", "type": "dimension", "parameters": [{"id": "frame_thickness", "name": "主框厚度", "type": "dimension", "dataType": "select", "options": ["70", "80", "90", "100"], "defaultValue": "80", "unit": "mm", "description": "主框型材厚度"}, {"id": "grid_frame_width", "name": "分格框宽度", "type": "dimension", "dataType": "select", "options": ["50", "60", "70"], "defaultValue": "60", "unit": "mm", "description": "分格框型材宽度"}], "constraints": [], "isRequired": false}, {"id": "rule_sash_configuration", "name": "窗扇配置", "type": "dimension", "parameters": [{"id": "operable_sash_count", "name": "开启扇数量", "type": "dimension", "dataType": "select", "options": ["0", "2", "4", "6", "8"], "defaultValue": "4", "description": "开启扇数量"}, {"id": "fixed_panel_count", "name": "固定面板数量", "type": "dimension", "dataType": "formula", "formula": "total_grid_count - operable_sash_count", "description": "固定面板数量"}], "constraints": [{"id": "constraint_sash_count", "expression": "operable_sash_count <= total_grid_count", "errorMessage": "开启扇数量不能超过总分格数", "severity": "error"}], "isRequired": false}, {"id": "rule_enhanced_glass_config", "name": "增强玻璃配置", "type": "material", "parameters": [{"id": "glass_thickness", "name": "玻璃厚度", "type": "material_property", "dataType": "select", "options": ["8", "10", "12", "15", "19"], "defaultValue": "10", "unit": "mm", "description": "大型防火窗玻璃厚度"}, {"id": "glass_weight_per_sqm", "name": "玻璃单位重量", "type": "material_property", "dataType": "formula", "formula": "glass_thickness * 2.5", "unit": "kg/m²", "description": "防火玻璃单位面积重量"}], "constraints": [{"id": "constraint_large_glass_thickness", "expression": "glass_thickness >= 8", "errorMessage": "大型防火窗玻璃厚度不少于8mm", "severity": "error"}], "isRequired": false}, {"id": "rule_heavy_duty_hardware", "name": "重型五金配置", "type": "material", "parameters": [{"id": "heavy_hinges_per_sash", "name": "每扇重型合页数", "type": "dimension", "dataType": "select", "options": ["3", "4", "5", "6"], "defaultValue": "4", "description": "每个开启扇的重型合页数量"}, {"id": "security_level", "name": "安全等级", "type": "material_property", "dataType": "select", "options": ["enhanced", "high", "ultra"], "defaultValue": "enhanced", "description": "大型防火窗安全等级"}, {"id": "has_auto_closing", "name": "自动闭窗", "type": "structural_property", "dataType": "boolean", "defaultValue": true, "description": "是否配置自动闭窗器"}], "constraints": [{"id": "constraint_heavy_hardware", "expression": "heavy_hinges_per_sash * 150 >= total_hardware_load / operable_sash_count", "errorMessage": "重型合页承重能力不足", "severity": "warning"}], "isRequired": false}], "defaultBOMTemplateId": "bom_template_fire_window_large", "defaultProcessRouteId": "process_route_fire_window_large", "costCalculationRules": [{"id": "cost_rule_material", "name": "物料成本", "type": "material", "formula": "sum(bom_item_cost)", "parameters": ["bom_item_cost"], "isActive": true}, {"id": "cost_rule_labor", "name": "人工成本", "type": "labor", "formula": "material_cost * 0.4", "parameters": ["material_cost"], "isActive": true}, {"id": "cost_rule_overhead", "name": "制造费用", "type": "overhead", "formula": "material_cost * 0.2", "parameters": ["material_cost"], "isActive": true}, {"id": "cost_rule_complexity", "name": "复杂度成本", "type": "complexity", "formula": "material_cost * 0.1 * (total_grid_count / 4)", "parameters": ["material_cost", "total_grid_count"], "isActive": true}], "isActive": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "template_fire_door_window_integrated", "name": "防火门窗一体化", "code": "FIRE_DOOR_WINDOW_INT", "category": "门窗", "productType": "custom", "description": "防火门窗一体化产品模板，集成防火门和防火窗功能，适用于复杂建筑场景", "structure": {"id": "structure_fire_door_window_integrated", "name": "防火门窗一体化结构", "components": [{"id": "component_integrated_frame", "name": "一体化框架", "type": "frame", "description": "防火门窗一体化主框架", "parameters": [{"id": "total_width", "name": "总宽度", "type": "dimension", "dataType": "formula", "formula": "door_width + window_width + middle_frame_width", "constraints": [], "unit": "mm"}, {"id": "total_height", "name": "总高度", "type": "dimension", "dataType": "formula", "formula": "max(door_height, window_height)", "constraints": [], "unit": "mm"}], "subComponents": [{"id": "subcomp_integrated_frame_profile", "name": "一体化框型材", "materialType": "profile", "parameters": [], "quantityFormula": "ceil((total_width + total_height) * 2 / 6000)", "materialSelectionRules": [{"id": "rule_integrated_frame_profile", "condition": "fire_rating >= 'A' && frame_thickness >= 80", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "integrated_frame", "thickness": {"min": 80}, "material": "steel", "strength_grade": "Q345"}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": true}, {"id": "component_door_section", "name": "门扇部分", "type": "sash", "description": "防火门扇部分", "parameters": [{"id": "door_clear_width", "name": "门净宽", "type": "dimension", "dataType": "formula", "formula": "door_width - frame_thickness * 2", "constraints": [], "unit": "mm"}, {"id": "door_clear_height", "name": "门净高", "type": "dimension", "dataType": "formula", "formula": "door_height - frame_thickness * 2", "constraints": [], "unit": "mm"}], "subComponents": [{"id": "subcomp_door_leaf_frame", "name": "门扇框架", "materialType": "profile", "parameters": [], "quantityFormula": "ceil((door_clear_width + door_clear_height) * 2 / 6000) * door_leaf_count", "materialSelectionRules": [{"id": "rule_door_leaf_frame", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "door_leaf", "material": "steel"}}, "priority": 1}]}, {"id": "subcomp_door_panel", "name": "门扇面板", "materialType": "panel", "parameters": [], "quantityFormula": "door_clear_width * door_clear_height / 1000000 * door_leaf_count", "materialSelectionRules": [{"id": "rule_door_panel", "condition": "fire_rating >= 'A' && door_panel_type == 'steel'", "materialCriteria": {"materialType": "panel", "requiredProperties": {"fire_rating": "A", "panel_type": "steel_door", "thickness": {"min": 1.2}}}, "priority": 1}]}, {"id": "subcomp_door_insulation", "name": "门扇保温层", "materialType": "insulation", "parameters": [], "quantityFormula": "door_clear_width * door_clear_height / 1000000 * door_leaf_count", "materialSelectionRules": [{"id": "rule_door_insulation", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "insulation", "requiredProperties": {"fire_rating": "A", "insulation_type": "mineral_wool", "density": {"min": 120}}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": true}, {"id": "component_window_section", "name": "窗扇部分", "type": "sash", "description": "防火窗扇部分", "parameters": [{"id": "window_clear_width", "name": "窗净宽", "type": "dimension", "dataType": "formula", "formula": "window_width - frame_thickness * 2", "constraints": [], "unit": "mm"}, {"id": "window_clear_height", "name": "窗净高", "type": "dimension", "dataType": "formula", "formula": "window_height - frame_thickness * 2", "constraints": [], "unit": "mm"}], "subComponents": [{"id": "subcomp_window_sash_frame", "name": "窗扇框架", "materialType": "profile", "parameters": [], "quantityFormula": "ceil((window_clear_width + window_clear_height) * 2 / 6000) * window_sash_count", "materialSelectionRules": [{"id": "rule_window_sash_frame", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "window_sash", "material": "steel"}}, "priority": 1}]}, {"id": "subcomp_integrated_fire_glass", "name": "一体化防火玻璃", "materialType": "glass", "parameters": [], "quantityFormula": "(window_clear_width - glass_margin * 2) * (window_clear_height - glass_margin * 2) / 1000000 * window_sash_count", "materialSelectionRules": [{"id": "rule_integrated_fire_glass", "condition": "fire_rating >= 'A' && glass_thickness >= 8", "materialCriteria": {"materialType": "glass", "requiredProperties": {"fire_rating": "A", "glass_type": "fire_resistant", "thickness": {"min": 8}}, "preferredProperties": {"transparency": "clear", "fire_resistance_time": {"min": 60}}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": true}, {"id": "component_separation_frame", "name": "分隔框", "type": "frame", "description": "门窗之间的分隔框架", "parameters": [{"id": "separation_frame_height", "name": "分隔框高度", "type": "dimension", "dataType": "formula", "formula": "max(door_height, window_height)", "constraints": [], "unit": "mm"}], "subComponents": [{"id": "subcomp_separation_profile", "name": "分隔框型材", "materialType": "profile", "parameters": [], "quantityFormula": "ceil(separation_frame_height / 6000)", "materialSelectionRules": [{"id": "rule_separation_profile", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "profile", "requiredProperties": {"fire_rating": "A", "profile_type": "separation", "material": "steel"}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": true}, {"id": "component_integrated_hardware", "name": "一体化五金系统", "type": "hardware", "description": "门窗一体化专用五金系统", "parameters": [{"id": "total_system_weight", "name": "系统总重量", "type": "structural_property", "dataType": "formula", "formula": "door_weight_estimate + window_weight_estimate", "constraints": [], "unit": "kg"}], "subComponents": [{"id": "subcomp_door_hinges", "name": "门合页", "materialType": "hardware", "parameters": [], "quantityFormula": "door_leaf_count * door_hinges_per_leaf", "materialSelectionRules": [{"id": "rule_door_hinges", "condition": "fire_rating >= 'A' && door_weight_estimate <= door_hinge_capacity * door_hinges_per_leaf", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "door_hinge", "material": "stainless_steel", "load_capacity": {"min": 120}}}, "priority": 1}]}, {"id": "subcomp_window_hinges", "name": "窗合页", "materialType": "hardware", "parameters": [], "quantityFormula": "window_sash_count * window_hinges_per_sash", "materialSelectionRules": [{"id": "rule_window_hinges", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "window_hinge", "material": "stainless_steel", "load_capacity": {"min": 80}}}, "priority": 1}]}, {"id": "subcomp_door_lock_system", "name": "门锁系统", "materialType": "hardware", "parameters": [], "quantityFormula": "door_leaf_count", "materialSelectionRules": [{"id": "rule_door_lock_system", "condition": "fire_rating >= 'A' && security_level >= 'high'", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "door_lock", "security_grade": "AA", "material": "stainless_steel"}}, "priority": 1}]}, {"id": "subcomp_integrated_closer", "name": "一体化闭门器", "materialType": "hardware", "parameters": [], "quantityFormula": "door_leaf_count + (has_window_auto_closing ? window_sash_count : 0)", "materialSelectionRules": [{"id": "rule_integrated_closer", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "hardware", "requiredProperties": {"fire_rating": "A", "hardware_type": "integrated_closer", "activation_temperature": {"max": 70}}}, "priority": 1}]}, {"id": "subcomp_comprehensive_sealant", "name": "综合密封系统", "materialType": "sealant", "parameters": [], "quantityFormula": "((total_width + total_height) * 2 + (door_clear_width + door_clear_height) * 2 + (window_clear_width + window_clear_height) * 2) / 1000", "materialSelectionRules": [{"id": "rule_comprehensive_sealant", "condition": "fire_rating >= 'A'", "materialCriteria": {"materialType": "sealant", "requiredProperties": {"fire_rating": "A", "sealant_type": "comprehensive", "temperature_resistance": {"min": 200}}}, "priority": 1}]}], "quantityFormula": "1", "isRequired": true, "isConfigurable": false}]}, "configurationRules": [{"id": "rule_integrated_dimensions", "name": "一体化尺寸配置", "type": "dimension", "parameters": [{"id": "door_width", "name": "门宽", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 1000, "minValue": 800, "maxValue": 1500, "description": "防火门宽度"}, {"id": "door_height", "name": "门高", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 2100, "minValue": 2000, "maxValue": 2400, "description": "防火门高度"}, {"id": "window_width", "name": "窗宽", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 1500, "minValue": 800, "maxValue": 2500, "description": "防火窗宽度"}, {"id": "window_height", "name": "窗高", "type": "dimension", "dataType": "number", "unit": "mm", "defaultValue": 1800, "minValue": 1200, "maxValue": 2400, "description": "防火窗高度"}, {"id": "middle_frame_width", "name": "中间框宽", "type": "dimension", "dataType": "select", "options": ["100", "120", "150"], "defaultValue": "120", "unit": "mm", "description": "门窗之间分隔框宽度"}], "constraints": [{"id": "constraint_integrated_total_width", "expression": "total_width <= 4000", "errorMessage": "一体化门窗总宽度不能超过4米", "severity": "error"}, {"id": "constraint_height_compatibility", "expression": "abs(door_height - window_height) <= 300", "errorMessage": "门窗高度差不应超过300mm", "severity": "warning"}], "isRequired": true}, {"id": "rule_integrated_fire_performance", "name": "一体化防火性能", "type": "material", "parameters": [{"id": "fire_rating", "name": "防火等级", "type": "material_property", "dataType": "select", "options": ["A", "A+"], "defaultValue": "A", "description": "一体化门窗防火等级"}, {"id": "fire_resistance_time", "name": "耐火时间", "type": "material_property", "dataType": "select", "options": ["60", "90", "120"], "defaultValue": "60", "unit": "分钟", "description": "一体化门窗耐火极限时间"}], "constraints": [{"id": "constraint_integrated_fire_consistency", "expression": "fire_rating == 'A' && fire_resistance_time >= 60", "errorMessage": "A级一体化门窗耐火时间不少于60分钟", "severity": "error"}], "isRequired": true}, {"id": "rule_integrated_structure", "name": "一体化结构配置", "type": "dimension", "parameters": [{"id": "frame_thickness", "name": "主框厚度", "type": "dimension", "dataType": "select", "options": ["80", "90", "100"], "defaultValue": "90", "unit": "mm", "description": "一体化主框厚度"}, {"id": "door_leaf_count", "name": "门扇数量", "type": "dimension", "dataType": "select", "options": ["1", "2"], "defaultValue": "1", "description": "防火门扇数量"}, {"id": "window_sash_count", "name": "窗扇数量", "type": "dimension", "dataType": "select", "options": ["1", "2", "3"], "defaultValue": "2", "description": "防火窗扇数量"}], "constraints": [], "isRequired": false}, {"id": "rule_door_panel_config", "name": "门扇面板配置", "type": "material", "parameters": [{"id": "door_panel_type", "name": "门扇面板类型", "type": "material_property", "dataType": "select", "options": ["steel", "steel_glass", "full_glass"], "defaultValue": "steel", "description": "门扇面板类型"}, {"id": "door_weight_estimate", "name": "门扇重量估算", "type": "structural_property", "dataType": "formula", "formula": "door_clear_width * door_clear_height / 1000000 * 45 * door_leaf_count", "unit": "kg", "description": "门扇重量估算"}], "constraints": [], "isRequired": false}, {"id": "rule_window_glass_config", "name": "窗玻璃配置", "type": "material", "parameters": [{"id": "glass_thickness", "name": "玻璃厚度", "type": "material_property", "dataType": "select", "options": ["8", "10", "12", "15"], "defaultValue": "8", "unit": "mm", "description": "防火窗玻璃厚度"}, {"id": "glass_margin", "name": "玻璃边距", "type": "dimension", "dataType": "number", "defaultValue": 25, "minValue": 20, "maxValue": 35, "unit": "mm", "description": "玻璃与窗框边距"}, {"id": "window_weight_estimate", "name": "窗扇重量估算", "type": "structural_property", "dataType": "formula", "formula": "(window_clear_width * window_clear_height / 1000000) * (glass_thickness * 2.5 + 15) * window_sash_count", "unit": "kg", "description": "窗扇重量估算"}], "constraints": [], "isRequired": false}, {"id": "rule_integrated_hardware_config", "name": "一体化五金配置", "type": "material", "parameters": [{"id": "door_hinges_per_leaf", "name": "每扇门合页数", "type": "dimension", "dataType": "select", "options": ["3", "4", "5"], "defaultValue": "4", "description": "每个门扇的合页数量"}, {"id": "window_hinges_per_sash", "name": "每扇窗合页数", "type": "dimension", "dataType": "select", "options": ["2", "3", "4"], "defaultValue": "3", "description": "每个窗扇的合页数量"}, {"id": "door_hinge_capacity", "name": "门合页承重", "type": "material_property", "dataType": "select", "options": ["120", "150", "180"], "defaultValue": "150", "unit": "kg", "description": "单个门合页承重能力"}, {"id": "security_level", "name": "安全等级", "type": "material_property", "dataType": "select", "options": ["high", "ultra", "premium"], "defaultValue": "high", "description": "一体化门窗安全等级"}, {"id": "has_window_auto_closing", "name": "窗扇自动闭合", "type": "structural_property", "dataType": "boolean", "defaultValue": true, "description": "窗扇是否配置自动闭合功能"}], "constraints": [{"id": "constraint_door_hinge_capacity", "expression": "door_hinge_capacity * door_hinges_per_leaf >= door_weight_estimate / door_leaf_count", "errorMessage": "门合页承重能力不足", "severity": "warning"}], "isRequired": false}], "defaultBOMTemplateId": "bom_template_fire_door_window_integrated", "defaultProcessRouteId": "process_route_fire_door_window_integrated", "costCalculationRules": [{"id": "cost_rule_material", "name": "物料成本", "type": "material", "formula": "sum(bom_item_cost)", "parameters": ["bom_item_cost"], "isActive": true}, {"id": "cost_rule_labor", "name": "人工成本", "type": "labor", "formula": "material_cost * 0.45", "parameters": ["material_cost"], "isActive": true}, {"id": "cost_rule_overhead", "name": "制造费用", "type": "overhead", "formula": "material_cost * 0.25", "parameters": ["material_cost"], "isActive": true}, {"id": "cost_rule_integration_complexity", "name": "一体化复杂度成本", "type": "complexity", "formula": "material_cost * 0.15", "parameters": ["material_cost"], "isActive": true}, {"id": "cost_rule_customization", "name": "定制化成本", "type": "customization", "formula": "material_cost * 0.1", "parameters": ["material_cost"], "isActive": true}], "isActive": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}]}