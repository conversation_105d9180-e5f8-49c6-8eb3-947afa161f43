{"f_outer_frame_width": {"description": "外框宽度 = 窗宽", "expression": {"type": "parameter", "id": "window_width"}}, "f_outer_frame_height": {"description": "外框高度 = 窗高", "expression": {"type": "parameter", "id": "window_height"}}, "f_sash_width": {"description": "扇宽度 = (外框宽 - 框厚*3) / 2", "expression": {"operator": "divide", "operands": [{"operator": "subtract", "operands": [{"type": "formula", "id": "f_outer_frame_width"}, {"operator": "multiply", "operands": [{"type": "parameter", "id": "frame_thickness"}, {"type": "literal", "value": 3}]}]}, {"type": "literal", "value": 2}]}}, "f_sash_height": {"description": "扇高度 = 外框高 - 框厚*2", "expression": {"operator": "subtract", "operands": [{"type": "formula", "id": "f_outer_frame_height"}, {"operator": "multiply", "operands": [{"type": "parameter", "id": "frame_thickness"}, {"type": "literal", "value": 2}]}]}}, "f_sash_y": {"description": "扇Y坐标 = 框厚", "expression": {"type": "parameter", "id": "frame_thickness"}}, "f_sash_left_x": {"description": "左扇X坐标 = 框厚", "expression": {"type": "parameter", "id": "frame_thickness"}}, "f_sash_right_x": {"description": "右扇X坐标 = 框厚*2 + 扇宽", "expression": {"operator": "add", "operands": [{"operator": "multiply", "operands": [{"type": "parameter", "id": "frame_thickness"}, {"type": "literal", "value": 2}]}, {"type": "formula", "id": "f_sash_width"}]}}, "f_glass_area_sqm": {"description": "玻璃面积（平方米） = (外框宽 - 边距) * (外框高 - 边距) / 1,000,000", "expression": {"operator": "divide", "operands": [{"operator": "multiply", "operands": [{"operator": "subtract", "operands": [{"type": "formula", "id": "f_outer_frame_width"}, {"type": "parameter", "id": "glass_margin"}]}, {"operator": "subtract", "operands": [{"type": "formula", "id": "f_outer_frame_height"}, {"type": "parameter", "id": "glass_margin"}]}]}, {"type": "literal", "value": 1000000}]}}, "f_glass_weight": {"description": "玻璃重量 (kg) = 玻璃面积 * 玻璃密度", "expression": {"operator": "multiply", "operands": [{"type": "formula", "id": "f_glass_area_sqm"}, {"type": "parameter", "id": "glass_density"}]}}}