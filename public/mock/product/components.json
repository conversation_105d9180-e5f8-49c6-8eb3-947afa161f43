{"components": [{"id": "comp_001", "code": "FRAME_OUTER_VERTICAL", "name": "外框立柱", "description": "防火窗外框立柱组件，承载主要结构荷载", "componentType": "frame", "materialCategoryId": "cat_steel_profile", "materialCategoryName": "钢质型材", "materialCategoryCode": "STEEL_PROFILE", "parameters": [{"id": "param_width", "name": "width", "displayName": "型材宽度", "type": "number", "unit": "mm", "defaultValue": 80, "minValue": 60, "maxValue": 120, "required": true, "description": "立柱型材的宽度尺寸", "category": "dimension"}, {"id": "param_thickness", "name": "thickness", "displayName": "壁厚", "type": "number", "unit": "mm", "defaultValue": 4.0, "minValue": 3.0, "maxValue": 6.0, "required": true, "description": "型材壁厚，影响承载能力", "category": "dimension"}, {"id": "param_material_grade", "name": "materialGrade", "displayName": "材质等级", "type": "select", "defaultValue": "Q235B", "options": [{"value": "Q235B", "label": "Q235B普通钢"}, {"value": "Q355B", "label": "Q355B高强钢"}, {"value": "304SS", "label": "304不锈钢"}, {"value": "316SS", "label": "316不锈钢"}], "required": true, "description": "钢材材质等级", "category": "material"}, {"id": "param_surface_treatment", "name": "surfaceTreatment", "displayName": "表面处理", "type": "select", "defaultValue": "galvanized", "options": [{"value": "galvanized", "label": "热镀锌"}, {"value": "powder_coating", "label": "粉末喷涂"}, {"value": "anodizing", "label": "阳极氧化"}, {"value": "brushed", "label": "拉丝处理"}], "required": true, "description": "表面防腐处理方式", "category": "process"}], "quantityFormula": "ceiling(window_height / 6000) * 2", "costFormula": "material_cost * (1 + processing_rate) * quantity", "constraints": [{"id": "const_001", "name": "承载能力约束", "type": "dimension", "expression": "thickness >= 3.0 && (window_height * window_width > 4000000 ? thickness >= 4.0 : true)", "errorMessage": "大尺寸窗户需要更厚的型材以确保承载能力", "severity": "error", "autoFix": {"enabled": true, "fixExpression": "thickness = window_height * window_width > 4000000 ? 5.0 : 4.0", "fixMessage": "自动调整型材厚度以满足承载要求"}}, {"id": "const_002", "name": "防火等级约束", "type": "material", "expression": "fire_rating === 'A' ? materialGrade !== 'Q235B' : true", "errorMessage": "A级防火要求使用高等级钢材", "severity": "warning"}], "processRequirements": [{"id": "proc_001", "processName": "切割", "workstation": "cutting_station", "estimatedTime": 15, "requirements": {"precision": "±1mm", "surfaceQuality": "Ra3.2", "toolType": "carbide_saw"}}, {"id": "proc_002", "processName": "端面加工", "workstation": "machining_station", "estimatedTime": 10, "requirements": {"precision": "±0.5mm", "perpendicularity": "0.1mm", "surfaceFinish": "smooth"}}], "properties": {"fireRating": "A", "structuralType": "load_bearing", "installationMethod": "welding", "maintenanceLevel": "low"}, "version": 1, "status": "active", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "createdBy": "engineer_001", "updatedBy": "engineer_001"}, {"id": "comp_002", "code": "FRAME_OUTER_HORIZONTAL", "name": "外框横梁", "description": "防火窗外框横梁组件，连接立柱形成框架", "componentType": "frame", "materialCategoryId": "cat_steel_profile", "materialCategoryName": "钢质型材", "materialCategoryCode": "STEEL_PROFILE", "parameters": [{"id": "param_width", "name": "width", "displayName": "型材宽度", "type": "number", "unit": "mm", "defaultValue": 80, "minValue": 60, "maxValue": 120, "required": true, "description": "横梁型材的宽度尺寸", "category": "dimension"}, {"id": "param_thickness", "name": "thickness", "displayName": "壁厚", "type": "number", "unit": "mm", "defaultValue": 4.0, "minValue": 3.0, "maxValue": 6.0, "required": true, "description": "型材壁厚", "category": "dimension"}], "quantityFormula": "ceiling(window_width / 6000) * 2", "constraints": [{"id": "const_003", "name": "跨度约束", "type": "dimension", "expression": "window_width <= 3000 || thickness >= 5.0", "errorMessage": "大跨度横梁需要增加壁厚", "severity": "error"}], "processRequirements": [{"id": "proc_003", "processName": "切割", "workstation": "cutting_station", "estimatedTime": 12, "requirements": {"precision": "±1mm", "surfaceQuality": "Ra3.2"}}], "properties": {"fireRating": "A", "structuralType": "load_bearing"}, "version": 1, "status": "active", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "createdBy": "engineer_001", "updatedBy": "engineer_001"}, {"id": "comp_003", "code": "GLASS_PANEL_FIRE", "name": "防火玻璃面板", "description": "防火玻璃面板，提供透明度和防火性能", "componentType": "glass", "materialCategoryId": "cat_fire_glass", "materialCategoryName": "防火玻璃", "materialCategoryCode": "FIRE_GLASS", "parameters": [{"id": "param_thickness", "name": "thickness", "displayName": "玻璃厚度", "type": "number", "unit": "mm", "defaultValue": 6, "minValue": 5, "maxValue": 19, "required": true, "description": "单片玻璃厚度", "category": "dimension"}, {"id": "param_glass_type", "name": "glassType", "displayName": "玻璃类型", "type": "select", "defaultValue": "single", "options": [{"value": "single", "label": "单片防火玻璃"}, {"value": "laminated", "label": "夹胶防火玻璃"}, {"value": "insulated", "label": "中空防火玻璃"}], "required": true, "description": "防火玻璃的结构类型", "category": "material"}, {"id": "param_fire_resistance_time", "name": "fireResistanceTime", "displayName": "耐火时间", "type": "select", "defaultValue": 60, "options": [{"value": 30, "label": "30分钟"}, {"value": 60, "label": "60分钟"}, {"value": 90, "label": "90分钟"}, {"value": 120, "label": "120分钟"}], "required": true, "description": "防火玻璃的耐火时间", "category": "quality"}], "quantityFormula": "(window_width - frame_width * 2) * (window_height - frame_width * 2) / 1000000", "constraints": [{"id": "const_004", "name": "防火等级匹配", "type": "material", "expression": "fire_rating === 'A' ? fireResistanceTime >= 60 : fireResistanceTime >= 30", "errorMessage": "防火等级与耐火时间不匹配", "severity": "error"}], "processRequirements": [{"id": "proc_004", "processName": "切割", "workstation": "glass_cutting", "estimatedTime": 20, "requirements": {"precision": "±2mm", "edgeQuality": "polished"}}], "properties": {"transparency": "high", "fireRating": "A", "impactResistance": "medium"}, "version": 1, "status": "active", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "createdBy": "engineer_001", "updatedBy": "engineer_001"}]}