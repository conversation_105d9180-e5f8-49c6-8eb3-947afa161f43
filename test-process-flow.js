// 简单的测试脚本来验证工艺流程生成器
console.log('测试工艺流程生成器...')

// 模拟规格数据
const testSpecs = [
  {
    length: 1800,
    width: 1200,
    thickness: 6,
    glassType: 'clear',
    color: '透明'
  },
  {
    length: 2400,
    width: 1600,
    thickness: 8,
    glassType: 'low_e',
    color: '透明'
  },
  {
    length: 1500,
    width: 1000,
    thickness: 5,
    glassType: 'tempered',
    color: '透明'
  }
]

// 模拟工艺流程生成函数
function generateStandardProcessFlow(specs) {
  const { length, width, thickness, glassType } = specs
  const area = (length * width) / 1000000 // 平方米
  
  const processFlow = []
  
  // 基础工艺步骤
  processFlow.push({
    stepName: '切割',
    workstation: length > 2000 ? 'cutting_station_2' : 'cutting_station_1',
    estimatedDuration: Math.ceil(area * 2 + thickness * 0.5),
    constraints: {},
    status: 'pending'
  })
  
  processFlow.push({
    stepName: '磨边',
    workstation: thickness >= 8 ? 'edging_station_2' : 'edging_station_1',
    estimatedDuration: Math.ceil(area * 3 + thickness * 0.8),
    constraints: {},
    status: 'pending'
  })
  
  // 特殊工艺
  if (glassType === 'low_e' || glassType === 'reflective') {
    processFlow.push({
      stepName: '镀膜',
      workstation: 'coating_station',
      estimatedDuration: Math.ceil(area * 5),
      constraints: { clean_environment: true },
      status: 'pending'
    })
  }
  
  if (thickness >= 5 || glassType === 'tempered' || glassType === 'low_e') {
    processFlow.push({
      stepName: '钢化',
      workstation: 'tempering_furnace_1',
      estimatedDuration: Math.ceil(area * 8 + 30),
      constraints: { temperature_control: true },
      status: 'pending'
    })
  }
  
  // 必须步骤
  processFlow.push({
    stepName: '质检',
    workstation: 'quality_station',
    estimatedDuration: Math.ceil(area * 1.5 + 5),
    constraints: {},
    status: 'pending'
  })
  
  processFlow.push({
    stepName: '包装',
    workstation: 'packaging_station',
    estimatedDuration: Math.ceil(area * 1 + 3),
    constraints: {},
    status: 'pending'
  })
  
  return processFlow
}

// 测试每个规格
testSpecs.forEach((specs, index) => {
  console.log(`\\n=== 测试规格 ${index + 1} ===`)
  console.log('规格:', specs)
  
  const processFlow = generateStandardProcessFlow(specs)
  console.log('生成的工艺流程:')
  processFlow.forEach((step, stepIndex) => {
    console.log(`  ${stepIndex + 1}. ${step.stepName} - ${step.workstation} (${step.estimatedDuration}分钟)`)
  })
  
  const totalTime = processFlow.reduce((sum, step) => sum + step.estimatedDuration, 0)
  console.log(`总工时: ${totalTime}分钟 (${(totalTime / 60).toFixed(1)}小时)`)
})

console.log('\\n✅ 工艺流程生成器测试完成')